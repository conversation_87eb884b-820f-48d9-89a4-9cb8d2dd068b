<!-- Streaming Header with Video Background -->
<div class="relative mb-8 rounded-lg overflow-hidden">
  <!-- Video Background (fallback to image if video doesn't load) -->
  <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://media.istockphoto.com/id/1256247098/photo/basketball-on-court.webp?a=1&b=1&s=612x612&w=0&k=20&c=BdSE9CsyYBadCZfxlwKyTfy_4PEqNMbhliSHZ0kdpPU=');">
    <div class="absolute inset-0 bg-black opacity-50"></div>
  </div>

  <!-- Content -->
  <div class="relative py-12 px-6 md:px-12 text-center">
    <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">Live Sports Streaming</h1>
    <p class="text-xl text-gray-200 max-w-3xl mx-auto mb-6">Watch your favorite sports events live from anywhere in the world.</p>

    <% if (!user) { %>
      <div class="bg-white/10 backdrop-blur-sm text-white p-4 rounded-lg mb-6 max-w-2xl mx-auto border border-white/20">
        <p class="text-lg">
          <i class="fas fa-lock mr-2"></i>
          Please <a href="/auth/login?redirect=/streaming" class="font-bold text-blue-300 hover:text-blue-200 underline">login</a> or
          <a href="/auth/register?redirect=/streaming" class="font-bold text-blue-300 hover:text-blue-200 underline">register</a>
          to unlock all live streaming features.
        </p>
      </div>
    <% } %>

    <!-- Sports categories filter -->
    <div class="flex flex-wrap justify-center gap-2 mt-6">
      <a href="/streaming" class="<%= !selectedCategory ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30' %> px-6 py-2 rounded-full text-sm font-semibold transition duration-300">
        All Sports
      </a>
      <% if (locals.categories && categories.length > 0) { %>
        <% categories.forEach(category => { %>
          <a href="/streaming?category=<%= category.id %>"
             class="<%= selectedCategory == category.id ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30' %> px-6 py-2 rounded-full text-sm font-semibold transition duration-300">
            <%= category.name %>
          </a>
        <% }) %>
      <% } %>
    </div>
  </div>
</div>

<!-- Live games section -->
<section class="mb-16">
  <div class="flex justify-between items-center mb-8">
    <div class="flex items-center">
      <h2 class="text-3xl font-bold text-gray-800 mr-3">Live Now</h2>
      <div class="bg-red-600 text-white text-xs px-3 py-1 rounded-full animate-pulse flex items-center">
        <span class="w-2 h-2 bg-white rounded-full mr-1"></span> LIVE
      </div>
    </div>
    <a href="#upcoming" class="text-blue-600 hover:text-blue-800 font-semibold flex items-center">
      Upcoming Games <i class="fas fa-arrow-down ml-1"></i>
    </a>
  </div>

  <% if (locals.liveGames && liveGames.length > 0) { %>
    <!-- Featured Live Game (first one) -->
    <div class="mb-10">
      <div class="bg-white rounded-xl shadow-xl overflow-hidden">
        <div class="md:flex">
          <div class="md:w-2/3 relative">
            <%
              // Use the local sport image for background
              const bgImage = '/images/sport.jpg';
            %>
            <div class="h-64 md:h-full bg-cover bg-center" style="background-image: url('<%= bgImage %>');">
              <div class="w-full h-full flex items-center justify-center bg-black bg-opacity-50">
                <div class="text-center">
                  <div class="bg-red-600 text-white text-sm font-bold px-4 py-1 rounded-full inline-flex items-center mb-4">
                    <span class="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></span> LIVE NOW
                  </div>
                  <a href="/streaming/<%= liveGames[0].id %>" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white text-5xl rounded-full w-20 h-20 flex items-center justify-center mx-auto transition duration-300 transform hover:scale-110">
                    <i class="fas fa-play"></i>
                  </a>
                </div>
              </div>
            </div>
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 md:hidden">
              <h3 class="text-white font-bold text-lg"><%= liveGames[0].home_team_name %> vs <%= liveGames[0].away_team_name %></h3>
              <p class="text-gray-300 text-sm"><%= liveGames[0].sport_name %></p>
            </div>
          </div>

          <div class="md:w-1/3 p-6">
            <div class="hidden md:block mb-4">
              <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                <%= liveGames[0].sport_name %>
              </span>
            </div>

            <h3 class="text-2xl font-bold text-gray-800 mb-4 hidden md:block">
              <%= liveGames[0].home_team_name %> vs <%= liveGames[0].away_team_name %>
            </h3>

            <div class="bg-gray-100 rounded-lg p-4 mb-6">
              <div class="flex justify-between items-center mb-4">
                <div class="flex flex-col items-center">
                  <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                  </div>
                  <span class="font-semibold text-sm"><%= liveGames[0].home_team_name %></span>
                </div>

                <div class="text-center">
                  <div class="text-3xl font-bold mb-1">
                    <span class="text-4xl"><%= liveGames[0].score_home || 0 %></span>
                    <span class="text-gray-400 mx-2">-</span>
                    <span class="text-4xl"><%= liveGames[0].score_away || 0 %></span>
                  </div>
                  <span class="text-xs text-red-600 animate-pulse">LIVE</span>
                </div>

                <div class="flex flex-col items-center">
                  <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-2">
                    <i class="fas fa-shield-alt text-red-600 text-2xl"></i>
                  </div>
                  <span class="font-semibold text-sm"><%= liveGames[0].away_team_name %></span>
                </div>
              </div>
            </div>

            <div class="text-sm text-gray-600 mb-6">
              <div class="flex items-center mb-2">
                <i class="fas fa-map-marker-alt w-5 text-center mr-2"></i>
                <span><%= liveGames[0].venue || 'TBA' %></span>
              </div>
              <div class="flex items-center">
                <i class="far fa-clock w-5 text-center mr-2"></i>
                <span>Started <%= new Date(liveGames[0].game_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %></span>
              </div>
            </div>

            <a href="/streaming/<%= liveGames[0].id %>" class="block w-full bg-red-600 hover:bg-red-700 text-white text-center font-bold py-3 px-4 rounded-lg transition duration-300">
              <i class="fas fa-play-circle mr-2"></i> Watch Live Stream
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Other Live Games -->
    <% if (liveGames.length > 1) { %>
      <h3 class="text-xl font-semibold text-gray-700 mb-4">More Live Games</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% liveGames.slice(1).forEach(game => { %>
          <div class="bg-white rounded-lg shadow-md overflow-hidden transition duration-300 hover:shadow-lg transform hover:-translate-y-1">
            <div class="relative">
              <%
                // Use the local sport image for background
                const gameBgImage = '/images/sport.jpg';
              %>
              <img src="<%= gameBgImage %>"
                   alt="<%= game.home_team_name %> vs <%= game.away_team_name %>"
                   class="w-full h-48 object-cover">
              <div class="absolute top-0 left-0 bg-red-600 text-white text-xs font-bold px-2 py-1 m-2 rounded-md flex items-center">
                <span class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></span> LIVE
              </div>
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                <h3 class="text-white font-bold"><%= game.home_team_name %> vs <%= game.away_team_name %></h3>
                <p class="text-gray-300 text-sm"><%= game.sport_name %></p>
              </div>
            </div>

            <div class="p-4">
              <div class="flex justify-between items-center mb-3">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shield-alt text-blue-600 text-xs"></i>
                  </div>
                  <span class="font-semibold mx-2"><%= game.score_home || 0 %></span>
                  <span class="text-gray-500">-</span>
                  <span class="font-semibold mx-2"><%= game.score_away || 0 %></span>
                  <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-shield-alt text-red-600 text-xs"></i>
                  </div>
                </div>

                <span class="text-sm text-gray-500">
                  <i class="fas fa-map-marker-alt mr-1"></i> <%= game.venue || 'TBA' %>
                </span>
              </div>

              <a href="/streaming/<%= game.id %>" class="block w-full bg-red-600 hover:bg-red-700 text-white text-center font-bold py-2 px-4 rounded transition duration-300">
                <i class="fas fa-play-circle mr-1"></i> Watch Now
              </a>
            </div>
          </div>
        <% }) %>
      </div>
    <% } %>
  <% } else { %>
    <div class="bg-white rounded-lg shadow-md p-8 text-center">
      <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <i class="fas fa-tv text-gray-400 text-3xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-700 mb-2">No Live Games Right Now</h3>
      <p class="text-gray-500 mb-6 max-w-md mx-auto">
        There are no live games at the moment. Check back later or browse upcoming games below.
      </p>
      <a href="#upcoming" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
        <i class="far fa-calendar-alt mr-2"></i> See Upcoming Games
      </a>
    </div>
  <% } %>
</section>

<!-- Upcoming games section -->
<section id="upcoming" class="pt-4">
  <div class="flex justify-between items-center mb-8">
    <h2 class="text-3xl font-bold text-gray-800">Upcoming Games</h2>
    <div class="flex items-center">
      <span class="text-sm text-gray-500 mr-2">Filter by:</span>
      <select id="categoryFilter" class="bg-white border border-gray-300 text-gray-700 py-1 px-3 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <option value="">All Sports</option>
        <% if (locals.categories && categories.length > 0) { %>
          <% categories.forEach(category => { %>
            <option value="<%= category.id %>" <%= selectedCategory == category.id ? 'selected' : '' %>><%= category.name %></option>
          <% }) %>
        <% } %>
      </select>
    </div>

    <script>
      // Make the filter dropdown functional
      document.getElementById('categoryFilter').addEventListener('change', function() {
        const categoryId = this.value;
        if (categoryId) {
          window.location.href = '/streaming?category=' + categoryId;
        } else {
          window.location.href = '/streaming';
        }
      });
    </script>
  </div>

  <% if (locals.upcomingGames && upcomingGames.length > 0) { %>
    <!-- Calendar view for upcoming games -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
      <div class="bg-blue-600 text-white p-4">
        <h3 class="font-bold text-lg">Upcoming Schedule</h3>
      </div>

      <div class="divide-y divide-gray-200">
        <% upcomingGames.forEach((game, index) => { %>
          <div class="p-5 hover:bg-gray-50 transition duration-200 <%= index % 2 === 0 ? 'bg-gray-50' : '' %>">
            <div class="md:flex md:justify-between md:items-center">
              <!-- Date and Time -->
              <div class="md:w-1/6 mb-4 md:mb-0">
                <div class="flex md:flex-col items-center md:items-start">
                  <div class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full mb-0 md:mb-2 mr-2 md:mr-0">
                    <%= game.sport_name %>
                  </div>
                  <div class="text-gray-700">
                    <div class="font-semibold">
                      <%= new Date(game.game_time).toLocaleDateString([], {month: 'short', day: 'numeric'}) %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= new Date(game.game_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Teams -->
              <div class="md:w-3/6 mb-4 md:mb-0">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-12 h-12 flex-shrink-0 mr-3 bg-blue-100 rounded-full flex items-center justify-center">
                      <i class="fas fa-shield-alt text-blue-600"></i>
                    </div>
                    <span class="font-semibold"><%= game.home_team_name %></span>
                  </div>

                  <div class="text-center mx-4">
                    <span class="text-gray-500 font-semibold">VS</span>
                  </div>

                  <div class="flex items-center">
                    <span class="font-semibold"><%= game.away_team_name %></span>
                    <div class="w-12 h-12 flex-shrink-0 ml-3 bg-red-100 rounded-full flex items-center justify-center">
                      <i class="fas fa-shield-alt text-red-600"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Venue and Actions -->
              <div class="md:w-2/6 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                  <i class="fas fa-map-marker-alt mr-1"></i> <%= game.venue || 'TBA' %>
                </div>

                <div class="flex space-x-2">
                  <a href="/streaming/<%= game.id %>" class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full transition duration-300">
                    <i class="far fa-bell mr-1"></i> Remind
                  </a>
                  <a href="/streaming/<%= game.id %>" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold px-3 py-1 rounded-full transition duration-300">
                    Details
                  </a>
                </div>
              </div>
            </div>
          </div>
        <% }) %>
      </div>
    </div>

    <!-- Weekly calendar view -->
    <div class="mb-8">
      <h3 class="text-xl font-semibold text-gray-700 mb-4">This Week's Games</h3>

      <div class="grid grid-cols-7 gap-2 text-center mb-2">
        <%
          const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
          const today = new Date();
          const currentDay = today.getDay();

          // Generate the week days
          for (let i = 0; i < 7; i++) {
            const dayIndex = (currentDay + i) % 7;
            const date = new Date(today);
            date.setDate(today.getDate() + i);
            const isToday = i === 0;
        %>
          <div class="<%= isToday ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700' %> rounded-t-lg p-2">
            <div class="font-semibold"><%= days[dayIndex] %></div>
            <div class="text-sm <%= isToday ? 'text-white' : 'text-gray-500' %>">
              <%= date.getDate() %>/<%= date.getMonth() + 1 %>
            </div>
          </div>
        <% } %>
      </div>

      <div class="grid grid-cols-7 gap-2 bg-white rounded-b-lg shadow-md p-2">
        <%
          // Create a 7-day array to hold games for each day
          const weekGames = Array(7).fill().map(() => []);

          // Distribute games to their respective days
          if (locals.upcomingGames) {
            upcomingGames.forEach(game => {
              const gameDate = new Date(game.game_time);
              const diffDays = Math.floor((gameDate - today) / (1000 * 60 * 60 * 24));

              if (diffDays >= 0 && diffDays < 7) {
                weekGames[diffDays].push(game);
              }
            });
          }

          // Display games for each day
          for (let i = 0; i < 7; i++) {
            const dayGames = weekGames[i];
        %>
          <div class="min-h-[100px] border border-gray-200 rounded p-1 text-left">
            <% if (dayGames.length > 0) { %>
              <% dayGames.slice(0, 2).forEach(game => { %>
                <a href="/streaming/<%= game.id %>" class="block text-xs p-1 mb-1 rounded hover:bg-gray-100">
                  <div class="font-semibold truncate"><%= game.home_team_name %> vs <%= game.away_team_name %></div>
                  <div class="text-gray-500"><%= new Date(game.game_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) %></div>
                </a>
              <% }) %>

              <% if (dayGames.length > 2) { %>
                <div class="text-xs text-center text-blue-600">+<%= dayGames.length - 2 %> more</div>
              <% } %>
            <% } %>
          </div>
        <% } %>
      </div>
    </div>
  <% } else { %>
    <div class="bg-white rounded-lg shadow-md p-8 text-center">
      <div class="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
        <i class="far fa-calendar-alt text-gray-400 text-3xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-700 mb-2">No Upcoming Games</h3>
      <p class="text-gray-500 mb-6 max-w-md mx-auto">
        There are no upcoming games scheduled at the moment. Please check back later.
      </p>
    </div>
  <% } %>
</section>
