<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0 opacity-50">
    <div class="absolute -top-24 -left-24 w-96 h-96 bg-primary-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob"></div>
    <div class="absolute top-[30%] -right-24 w-96 h-96 bg-accent-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-24 left-[30%] w-96 h-96 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000"></div>
  </div>

  <div class="max-w-md w-full relative z-10" data-aos="fade-up" data-aos-duration="1000">
    <!-- Logo and title -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 shadow-glow mb-5 transform transition-all duration-500 hover:rotate-12 hover:scale-110">
        <i class="fas fa-basketball-ball text-white text-3xl"></i>
      </div>
      <h2 class="text-4xl font-bold font-heading text-secondary-900 mb-2">Welcome Back</h2>
      <p class="text-lg text-secondary-600">Sign in to access your SportZone account</p>
    </div>

    <!-- Card with form -->
    <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-strong border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-strong hover:scale-[1.01]">
      <div class="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-5 px-6 relative overflow-hidden">
        <!-- Decorative pattern -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>

        <h3 class="text-xl font-bold font-heading flex items-center relative z-10">
          <i class="fas fa-sign-in-alt mr-2"></i> Login to Your Account
        </h3>
      </div>

      <form action="/auth/login" method="POST" class="py-8 px-8">
        <input type="hidden" name="redirect" value="<%= locals.redirect || '/' %>">

        <% if (locals.error) { %>
          <div class="bg-red-100/80 backdrop-blur-sm border border-red-200 text-red-700 p-4 rounded-xl mb-6 animate-shake">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-red-100 rounded-full p-1">
                <i class="fas fa-exclamation-circle text-red-500"></i>
              </div>
              <p class="ml-3 text-sm font-medium"><%= error %></p>
            </div>
          </div>
        <% } %>

        <% if (locals.success) { %>
          <div class="bg-green-100/80 backdrop-blur-sm border border-green-200 text-green-700 p-4 rounded-xl mb-6">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-green-100 rounded-full p-1">
                <i class="fas fa-check-circle text-green-500"></i>
              </div>
              <p class="ml-3 text-sm font-medium"><%= success %></p>
            </div>
          </div>
        <% } %>

        <div class="mb-6 group">
          <label for="username" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Username or Email</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-user text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
            </div>
            <input type="text" id="username" name="username"
                  class="form-input w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 transition-all duration-300"
                  required autocomplete="username">
          </div>
        </div>

        <div class="mb-6 group">
          <label for="password" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
            </div>
            <input type="password" id="password" name="password"
                  class="form-input w-full pl-10 pr-10 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 transition-all duration-300"
                  required autocomplete="current-password">
            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
              <i class="fas fa-eye"></i>
            </button>
          </div>
        </div>

        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <input type="checkbox" id="remember" name="remember"
                  class="h-4 w-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500">
            <label for="remember" class="ml-2 text-sm text-secondary-600">Remember me</label>
          </div>
          <a href="/auth/forgot-password" class="text-sm text-primary-600 hover:text-primary-700 transition-colors duration-200 font-medium hover:underline">
            Forgot password?
          </a>
        </div>

        <button type="submit" class="btn-primary w-full py-3 rounded-xl flex items-center justify-center group relative overflow-hidden shadow-lg">
          <span class="relative z-10 font-semibold">Sign In</span>
          <i class="fas fa-arrow-right ml-2 relative z-10 transform group-hover:translate-x-1 transition-transform duration-200"></i>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 transition-all duration-300 group-hover:scale-105"></div>
        </button>

        <!-- Social login options -->
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-secondary-200"></div>
            </div>
          </div>

        <div class="mt-8 text-center text-sm text-secondary-600">
          Don't have an account?
          <a href="/auth/register<%= locals.redirect ? '?redirect=' + redirect : '' %>" class="text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200 hover:underline">
            Register now
          </a>
        </div>
      </form>
    </div>

    <!-- Security note -->
    <div class="mt-6 text-center text-xs text-secondary-500 flex items-center justify-center bg-white/30 backdrop-blur-sm py-2 px-4 rounded-full shadow-sm">
      <i class="fas fa-shield-alt mr-2 text-primary-500"></i>
      <span>Secure, encrypted connection</span>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');

    if (togglePassword && password) {
      togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);

        // Toggle icon
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
      });
    }

    // Input focus effects
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentNode.parentNode.classList.add('focused');
        this.classList.add('input-focused');
      });

      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentNode.parentNode.classList.remove('focused');
        }
        this.classList.remove('input-focused');
      });

      // Check if input has value on page load
      if (input.value) {
        input.parentNode.parentNode.classList.add('focused');
      }
    });

    // Subtle background animation
    const animateBackground = () => {
      const blobs = document.querySelectorAll('.animate-blob');
      blobs.forEach(blob => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        blob.style.transform = `translate(${randomX}px, ${randomY}px)`;
        setTimeout(() => {
          blob.style.transition = 'transform 15s ease-in-out';
        }, 100);
      });
    };

    // Run animation every 15 seconds
    animateBackground();
    setInterval(animateBackground, 15000);
  });
</script>

<style type="text/css">
.focused .form-label {
  color: #2563eb;
  transform: translateY(-0.5rem) scale(0.9);
  transform-origin: left top;
}

.form-label {
  transition: all 0.2s ease-out;
}

.group:focus-within .fas {
  color: #3b82f6;
}

.input-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px #dbeafe !important;
}

.animate-blob {
  animation: blob-bounce 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

@keyframes blob-bounce {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(20px, -20px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
}
</style>
