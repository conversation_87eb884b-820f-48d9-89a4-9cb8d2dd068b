<!-- Fantasy header with background -->
<div class="relative mb-12">
  <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl opacity-10"></div>
  <div class="relative py-8 px-6 rounded-2xl overflow-hidden" data-aos="fade-up" data-aos-duration="1000">
    <!-- Decorative elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
    <div class="absolute -bottom-8 -left-8 w-64 h-64 bg-blue-700 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>

    <div class="relative">
      <div class="flex flex-col md:flex-row md:items-center justify-between gap-6">
        <div>
          <h1 class="text-4xl font-bold font-heading text-secondary-900 mb-2 flex items-center">
            <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
              <i class="fas fa-trophy text-blue-600"></i>
            </div>
            Fantasy Sports Leagues
          </h1>
          <p class="text-secondary-600 max-w-2xl">Create your dream team, compete with friends, and prove your sports knowledge in our fantasy leagues!</p>
        </div>

        <div class="flex space-x-3">
          <a href="#my-teams" class="bg-white py-2.5 px-5 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
            <i class="fas fa-user mr-2 text-blue-600"></i> My Teams
          </a>
          <a href="#available-leagues" class="bg-blue-600 hover:bg-blue-700 py-2.5 px-5 rounded-xl text-sm font-medium text-white shadow-md hover:shadow-lg transition-all duration-200 flex items-center">
            <i class="fas fa-plus mr-2"></i> Join League
          </a>
        </div>
      </div>
    </div>
  </div>

  <% if (!user) { %>
    <div class="bg-blue-100/80 backdrop-blur-sm border border-blue-200 text-blue-800 p-5 rounded-xl mt-6 shadow-md animate-fade-in" data-aos="fade-up" data-aos-delay="200">
      <div class="flex items-start">
        <div class="flex-shrink-0 bg-blue-200 rounded-full p-2 mr-4">
          <i class="fas fa-info-circle text-blue-600 text-lg"></i>
        </div>
        <div>
          <h3 class="font-semibold text-blue-800 mb-1">Authentication Required</h3>
          <p class="mb-3">Please <a href="/auth/login?redirect=/fantasy" class="font-bold text-blue-700 hover:text-blue-800 hover:underline">login</a> or <a href="/auth/register?redirect=/fantasy" class="font-bold text-blue-700 hover:text-blue-800 hover:underline">register</a> to participate in fantasy leagues.</p>
          <div class="flex space-x-3 mt-2">
            <a href="/auth/login?redirect=/fantasy" class="bg-white py-2 px-4 rounded-lg text-sm font-medium text-blue-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
              <i class="fas fa-sign-in-alt mr-2"></i> Login
            </a>
            <a href="/auth/register?redirect=/fantasy" class="bg-blue-600 hover:bg-blue-700 py-2 px-4 rounded-lg text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
              <i class="fas fa-user-plus mr-2"></i> Register
            </a>
          </div>
        </div>
      </div>
    </div>
  <% } %>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
  <!-- User's teams section -->
  <div class="lg:col-span-2">
    <section id="my-teams" class="mb-8" data-aos="fade-up" data-aos-duration="1000">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
          <div class="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
            <i class="fas fa-users text-blue-600"></i>
          </div>
          My Fantasy Teams
        </h2>
        <button class="bg-white py-2 px-4 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
          <i class="fas fa-sort mr-2 text-blue-500"></i> Sort Teams
        </button>
      </div>

      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div class="p-6">
          <% if (locals.userTeams && userTeams.length > 0) { %>
            <div class="space-y-5">
              <% userTeams.forEach(team => { %>
                <div class="fantasy-team-card bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.01] overflow-hidden">
                  <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 relative">
                    <!-- Decorative pattern -->
                    <div class="absolute inset-0 opacity-10">
                      <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
                    </div>

                    <div class="flex justify-between items-center relative z-10">
                      <h3 class="font-bold text-lg"><%= team.name %></h3>
                      <span class="bg-white/20 backdrop-blur-sm text-white text-xs font-semibold px-2.5 py-1 rounded-full">
                        <%= team.sport_name %>
                      </span>
                    </div>
                  </div>

                  <div class="p-4">
                    <p class="text-secondary-600 mb-3 flex items-center">
                      <i class="fas fa-trophy text-blue-500 mr-2"></i>
                      League: <span class="font-medium ml-1"><%= team.league_name %></span>
                    </p>

                    <div class="flex justify-between items-center">
                      <div class="flex space-x-4">
                        <div class="bg-blue-50 rounded-lg px-3 py-1.5 flex items-center">
                          <i class="fas fa-star text-yellow-500 mr-2"></i>
                          <span class="text-secondary-700">Points: <strong><%= team.points %></strong></span>
                        </div>

                        <% if (team.team_rank) { %>
                          <div class="bg-blue-50 rounded-lg px-3 py-1.5 flex items-center">
                            <i class="fas fa-medal text-blue-500 mr-2"></i>
                            <span class="text-secondary-700">Rank: <strong>#<%= team.team_rank %></strong></span>
                          </div>
                        <% } %>
                      </div>

                      <a href="/fantasy/teams/<%= team.id %>" class="bg-blue-600 hover:bg-blue-700 py-2 px-4 rounded-lg text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 flex items-center group">
                        <span>Manage</span>
                        <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-200"></i>
                      </a>
                    </div>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div class="text-center py-12 px-4">
              <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-trophy text-blue-500 text-3xl"></i>
              </div>
              <h4 class="text-xl font-semibold text-secondary-900 mb-3">No Fantasy Teams Yet</h4>
              <p class="text-secondary-600 mb-8 max-w-md mx-auto">Join a fantasy league to start building your dream team and compete with other sports fans!</p>
              <a href="#available-leagues" class="bg-blue-600 hover:bg-blue-700 py-3 px-8 rounded-xl inline-flex items-center justify-center text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 group">
                <span>Browse Leagues</span>
                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-200"></i>
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </section>
  </div>

  <!-- Available leagues section -->
  <div>
    <section id="available-leagues" data-aos="fade-up" data-aos-delay="200" data-aos-duration="1000">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
          <div class="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
            <i class="fas fa-globe text-blue-600"></i>
          </div>
          Available Leagues
        </h2>
        <div class="relative">
          <select class="bg-white py-2 pl-4 pr-10 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>All Sports</option>
            <option>Football</option>
            <option>Basketball</option>
            <option>Cricket</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <i class="fas fa-chevron-down text-blue-500 text-xs"></i>
          </div>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div class="p-6">
          <% if (locals.availableLeagues && availableLeagues.length > 0) { %>
            <div class="space-y-5">
              <% availableLeagues.forEach(league => { %>
                <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.01] overflow-hidden">
                  <div class="p-5">
                    <div class="flex justify-between items-start mb-3">
                      <div>
                        <h3 class="font-bold text-lg text-secondary-900 flex items-center">
                          <%= league.name %>
                          <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-1 rounded-full">
                            <%= league.sport_name %>
                          </span>
                        </h3>
                        <p class="text-secondary-600 text-sm mt-1"><%= league.description %></p>
                      </div>
                    </div>

                    <div class="flex flex-wrap gap-3 mb-4">
                      <div class="bg-blue-50 rounded-lg px-3 py-1.5 flex items-center text-sm">
                        <i class="fas fa-users text-blue-500 mr-2"></i>
                        <span class="text-secondary-700"><%= league.team_count || 0 %>/<%= league.max_teams %> teams</span>
                      </div>

                      <% if (league.start_date) { %>
                        <div class="bg-blue-50 rounded-lg px-3 py-1.5 flex items-center text-sm">
                          <i class="far fa-calendar-alt text-blue-500 mr-2"></i>
                          <span class="text-secondary-700 format-date" data-date="<%= league.start_date %>">
                            <%= new Date(league.start_date).toLocaleDateString() %>
                          </span>
                        </div>
                      <% } %>
                    </div>

                    <!-- Progress bar showing how full the league is -->
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                      <div class="bg-blue-600 h-2 rounded-full" style="width: <%= Math.round((league.team_count || 0) / league.max_teams * 100) %>%"></div>
                    </div>

                    <a href="/fantasy/join/<%= league.id %>" class="w-full bg-blue-600 hover:bg-blue-700 py-2.5 px-4 rounded-lg text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center group">
                      <i class="fas fa-credit-card mr-2"></i>
                      <span>Join League ($<%= league.entry_fee || 'Free' %>)</span>
                    </a>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div class="text-center py-10 px-4">
              <div class="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-trophy text-blue-500 text-2xl"></i>
              </div>
              <h4 class="text-lg font-semibold text-secondary-900 mb-2">No Fantasy Leagues Available</h4>
              <p class="text-secondary-600 mb-4">New fantasy leagues will be available soon. Check back later!</p>
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <p class="text-sm text-blue-700">
                  <i class="fas fa-info-circle mr-2"></i>
                  Fantasy leagues are created by administrators. Stay tuned for upcoming tournaments!
                </p>
              </div>
            </div>
          <% } %>
        </div>
      </div>
    </section>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          // Add animation to the target section
          targetElement.classList.add('animate-section-focus');
          setTimeout(() => {
            targetElement.classList.remove('animate-section-focus');
          }, 1000);

          // Scroll to target with offset for header
          window.scrollTo({
            top: targetElement.offsetTop - 100,
            behavior: 'smooth'
          });
        }
      });
    });

    // Format dates to be more readable
    document.querySelectorAll('.format-date').forEach(dateElement => {
      const dateStr = dateElement.getAttribute('data-date');
      if (dateStr) {
        const date = new Date(dateStr);
        const options = { year: 'numeric', month: 'short', day: 'numeric' };
        dateElement.textContent = date.toLocaleDateString('en-US', options);
      }
    });

    // Animate progress bars on scroll
    const animateProgressBars = () => {
      const progressBars = document.querySelectorAll('.bg-blue-600.h-2.rounded-full');
      progressBars.forEach(bar => {
        if (isElementInViewport(bar) && !bar.classList.contains('animated')) {
          const width = bar.style.width;
          bar.style.width = '0%';
          bar.classList.add('animated');

          setTimeout(() => {
            bar.style.transition = 'width 1s ease-out';
            bar.style.width = width;
          }, 100);
        }
      });
    };

    // Check if element is in viewport
    function isElementInViewport(el) {
      const rect = el.getBoundingClientRect();
      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    }

    // Run on scroll
    window.addEventListener('scroll', animateProgressBars);
    // Run once on page load
    animateProgressBars();

    // Subtle background animation for blobs
    const animateBackground = () => {
      const blobs = document.querySelectorAll('.animate-blob');
      blobs.forEach(blob => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        blob.style.transform = `translate(${randomX}px, ${randomY}px)`;
        setTimeout(() => {
          blob.style.transition = 'transform 15s ease-in-out';
        }, 100);
      });
    };

    // Run animation every 15 seconds
    animateBackground();
    setInterval(animateBackground, 15000);
  });

  // Enhanced team creation with modal dialog instead of prompt
  function createFantasyTeam(leagueId) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in';

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'bg-white rounded-2xl shadow-xl max-w-md w-full mx-4 animate-slide-up';

    modal.innerHTML = `
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-6 rounded-t-2xl relative">
        <div class="absolute inset-0 opacity-10 rounded-t-2xl">
          <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>
        <h3 class="text-xl font-bold font-heading relative z-10">Create Your Fantasy Team</h3>
      </div>
      <div class="p-6">
        <p class="text-secondary-600 mb-4">Enter a name for your fantasy team to join this league.</p>
        <div class="mb-4">
          <label for="teamName" class="block text-sm font-medium text-secondary-700 mb-1 ml-1">Team Name</label>
          <input type="text" id="teamName" class="w-full px-4 py-3 rounded-xl border-secondary-200 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-300" placeholder="Enter team name...">
          <p id="nameError" class="text-red-500 text-sm mt-1 ml-1 hidden">Please enter a team name</p>
        </div>
        <div class="flex space-x-3 justify-end">
          <button id="cancelBtn" class="bg-white border border-secondary-200 hover:bg-secondary-50 py-2.5 px-5 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200">
            Cancel
          </button>
          <button id="createBtn" class="bg-blue-600 hover:bg-blue-700 py-2.5 px-5 rounded-xl text-sm font-medium text-white shadow-md hover:shadow-lg transition-all duration-200">
            Create Team
          </button>
        </div>
      </div>
    `;

    // Add modal to body
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Focus input
    setTimeout(() => {
      const input = document.getElementById('teamName');
      if (input) input.focus();
    }, 100);

    // Handle cancel
    document.getElementById('cancelBtn').addEventListener('click', () => {
      overlay.classList.add('animate-fade-out');
      modal.classList.add('animate-slide-down');
      setTimeout(() => {
        document.body.removeChild(overlay);
      }, 300);
    });

    // Handle click outside modal
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.classList.add('animate-fade-out');
        modal.classList.add('animate-slide-down');
        setTimeout(() => {
          document.body.removeChild(overlay);
        }, 300);
      }
    });

    // Handle create
    document.getElementById('createBtn').addEventListener('click', () => {
      const teamName = document.getElementById('teamName').value.trim();
      const errorElement = document.getElementById('nameError');

      if (!teamName) {
        errorElement.classList.remove('hidden');
        document.getElementById('teamName').classList.add('border-red-500');
        return;
      }

      // Show loading state
      const createBtn = document.getElementById('createBtn');
      createBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Creating...';
      createBtn.disabled = true;

      // Submit data
      fetch('/fantasy/teams', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: teamName, leagueId }),
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message before redirect
          modal.innerHTML = `
            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-4 px-6 rounded-t-2xl">
              <h3 class="text-xl font-bold font-heading">Team Created!</h3>
            </div>
            <div class="p-6 text-center">
              <div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
              </div>
              <p class="text-secondary-600 mb-6">Your team "${teamName}" has been created successfully!</p>
              <p class="text-secondary-500 text-sm">Redirecting to your team page...</p>
            </div>
          `;

          setTimeout(() => {
            window.location.href = `/fantasy/teams/${data.teamId}`;
          }, 1500);
        } else {
          errorElement.textContent = data.error || 'Failed to create team';
          errorElement.classList.remove('hidden');
          createBtn.innerHTML = 'Create Team';
          createBtn.disabled = false;
        }
      })
      .catch(error => {
        console.error('Error creating fantasy team:', error);
        errorElement.textContent = 'An error occurred. Please try again.';
        errorElement.classList.remove('hidden');
        createBtn.innerHTML = 'Create Team';
        createBtn.disabled = false;
      });
    });

    // Handle enter key
    document.getElementById('teamName').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        document.getElementById('createBtn').click();
      }
    });
  }
</script>

<style>
  @keyframes sectionFocus {
    0% { transform: scale(1); }
    50% { transform: scale(1.01); }
    100% { transform: scale(1); }
  }

  .animate-section-focus {
    animation: sectionFocus 0.5s ease-in-out;
  }

  @keyframes blob-bounce {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(20px, -20px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
  }

  .animate-blob {
    animation: blob-bounce 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out;
  }

  @keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideDown {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(50px); opacity: 0; }
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
</style>
