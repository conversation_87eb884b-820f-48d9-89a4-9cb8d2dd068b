const mysql = require('mysql2/promise');
require('dotenv').config();

async function addResetColumns() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'online_sports'
    });

    console.log('Connected to MySQL database');

    // Check if columns already exist
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME IN ('reset_token', 'reset_token_expiry')
    `, [process.env.DB_NAME || 'online_sports']);

    if (columns.length > 0) {
      console.log('Reset token columns already exist');
      return;
    }

    // Add the reset token columns
    await connection.query(`
      ALTER TABLE users 
      ADD COLUMN reset_token VARCHAR(255) NULL,
      ADD COLUMN reset_token_expiry TIMESTAMP NULL,
      ADD INDEX idx_reset_token (reset_token)
    `);

    console.log('Successfully added reset_token and reset_token_expiry columns to users table');

  } catch (error) {
    console.error('Error adding reset columns:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

addResetColumns();
