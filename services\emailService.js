// Email Service for sending password reset emails using Gmail SMTP
const nodemailer = require('nodemailer');

const sendPasswordResetEmail = async (email, resetLink) => {
  try {
    // Check if email credentials are configured
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      // Fallback to console logging if email not configured
      console.log('='.repeat(60));
      console.log('📧 PASSWORD RESET EMAIL (Email not configured)');
      console.log('='.repeat(60));
      console.log(`To: ${email}`);
      console.log(`Reset Link: ${resetLink}`);
      console.log('='.repeat(60));
      console.log('💡 To send real emails, add EMAIL_USER and EMAIL_PASS to your .env file');
      console.log('='.repeat(60));
      return true;
    }

    // Create Gmail SMTP transporter
    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    // Email template
    const mailOptions = {
      from: {
        name: 'Online Sports Website',
        address: process.env.EMAIL_USER
      },
      to: email,
      subject: '🔐 Password Reset Request - Online Sports',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🏆 Online Sports</h1>
            <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Password Reset Request</p>
          </div>

          <div style="background: #ffffff; padding: 30px; border: 1px solid #ddd; border-top: none;">
            <h2 style="color: #333; margin-top: 0;">Reset Your Password</h2>

            <p>Hello there! 👋</p>

            <p>We received a request to reset the password for your Online Sports account. If you made this request, click the button below to reset your password:</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                🔐 Reset My Password
              </a>
            </div>

            <p style="background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;">
              <strong>⏰ Important:</strong> This link will expire in <strong>1 hour</strong> for your security.
            </p>

            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all; font-family: monospace; font-size: 14px;">
              ${resetLink}
            </p>

            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

            <p style="color: #666; font-size: 14px;">
              <strong>🛡️ Security Note:</strong> If you didn't request this password reset, please ignore this email. Your password will remain unchanged.
            </p>

            <p style="color: #666; font-size: 14px;">
              Need help? Contact our support team or visit our website.
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; border: 1px solid #ddd; border-top: none;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              © 2024 Online Sports Website. All rights reserved.
            </p>
            <p style="margin: 5px 0 0 0; color: #999; font-size: 12px;">
              This email was sent to ${email}
            </p>
          </div>
        </body>
        </html>
      `
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);

    console.log('✅ Password reset email sent successfully!');
    console.log(`📧 Email sent to: ${email}`);
    console.log(`📨 Message ID: ${info.messageId}`);

    return true;

  } catch (error) {
    console.error('❌ Error sending password reset email:', error);

    // Fallback to console logging if email fails
    console.log('='.repeat(60));
    console.log('📧 PASSWORD RESET EMAIL (Fallback - Email failed)');
    console.log('='.repeat(60));
    console.log(`To: ${email}`);
    console.log(`Reset Link: ${resetLink}`);
    console.log('='.repeat(60));

    // Don't throw error - still allow password reset to work
    return true;
  }
};

module.exports = {
  sendPasswordResetEmail
};
