<div class="max-w-6xl mx-auto">
  <!-- Profile header banner -->
  <div class="relative mb-8 rounded-2xl overflow-hidden shadow-lg" data-aos="fade-down" data-aos-duration="1000">
    <div class="h-48 bg-gradient-to-r from-primary-600 via-primary-500 to-primary-700 relative">
      <!-- Decorative pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
      </div>

      <!-- Edit cover button -->
      <button class="absolute top-4 right-4 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200 rounded-full p-2" title="Edit Cover">
        <i class="fas fa-camera"></i>
      </button>
    </div>

    <!-- Profile info overlay -->
    <div class="absolute -bottom-16 left-8 flex items-end">
      <div class="relative">
        <div class="w-32 h-32 rounded-full bg-white p-1.5 shadow-xl">
          <div class="w-full h-full rounded-full bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center text-white">
            <span class="text-4xl font-bold"><%= user.username.charAt(0).toUpperCase() %></span>
          </div>
        </div>
        <button class="absolute bottom-1 right-1 bg-primary-600 text-white hover:bg-primary-700 transition-all duration-200 rounded-full p-2 shadow-md" title="Change Profile Picture">
          <i class="fas fa-camera"></i>
        </button>
      </div>

      <div class="mb-4 ml-4 hidden md:block">
        <h1 class="text-3xl font-bold font-heading text-white drop-shadow-md"><%= user.username %></h1>
        <p class="text-primary-100 flex items-center">
          <i class="fas fa-envelope mr-2"></i>
          <%= user.email %>
        </p>
      </div>
    </div>

    <!-- Stats cards -->
    <div class="absolute -bottom-12 right-8 hidden md:flex space-x-4">
      <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-3 flex items-center">
        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
          <i class="fas fa-trophy text-blue-600"></i>
        </div>
        <div>
          <p class="text-xs text-secondary-500">Fantasy Teams</p>
          <p class="text-lg font-bold text-secondary-900">0</p>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-3 flex items-center">
        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
          <i class="fas fa-medal text-green-600"></i>
        </div>
        <div>
          <p class="text-xs text-secondary-500">Achievements</p>
          <p class="text-lg font-bold text-secondary-900">3</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile profile info -->
  <div class="md:hidden text-center mb-16 mt-20">
    <h1 class="text-2xl font-bold font-heading text-secondary-900"><%= user.username %></h1>
    <p class="text-secondary-600 flex items-center justify-center">
      <i class="fas fa-envelope mr-2"></i>
      <%= user.email %>
    </p>
    <div class="mt-2 flex items-center justify-center space-x-3">
      <span class="bg-primary-100 text-primary-800 text-xs px-3 py-1 rounded-full">
        <i class="fas fa-user mr-1"></i> Member
      </span>
      <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">
        <i class="fas fa-trophy mr-1"></i> 0 Teams
      </span>
    </div>
  </div>

  <div class="flex flex-col md:flex-row items-start gap-8">
    <!-- Left sidebar with profile navigation -->
    <div class="w-full md:w-1/4" data-aos="fade-right" data-aos-duration="1000">
      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden sticky top-24">
        <!-- Profile navigation -->
        <div class="p-4">
          <nav>
            <ul class="space-y-2">
              <li>
                <a href="#account" class="flex items-center py-3 px-4 rounded-xl bg-primary-50 text-primary-700 font-medium transition-all duration-200 hover:shadow-md">
                  <div class="w-8 h-8 rounded-lg bg-primary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-user-circle text-primary-600"></i>
                  </div>
                  <span>Account Information</span>
                </a>
              </li>
              <li>
                <a href="#teams" class="flex items-center py-3 px-4 rounded-xl hover:bg-secondary-50 text-secondary-700 transition-all duration-200 hover:shadow-md">
                  <div class="w-8 h-8 rounded-lg bg-secondary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-trophy text-secondary-600"></i>
                  </div>
                  <span>My Fantasy Teams</span>
                </a>
              </li>
              <li>
                <a href="#activity" class="flex items-center py-3 px-4 rounded-xl hover:bg-secondary-50 text-secondary-700 transition-all duration-200 hover:shadow-md">
                  <div class="w-8 h-8 rounded-lg bg-secondary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-history text-secondary-600"></i>
                  </div>
                  <span>Activity</span>
                </a>
              </li>
              <li>
                <a href="#settings" class="flex items-center py-3 px-4 rounded-xl hover:bg-secondary-50 text-secondary-700 transition-all duration-200 hover:shadow-md">
                  <div class="w-8 h-8 rounded-lg bg-secondary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-cog text-secondary-600"></i>
                  </div>
                  <span>Settings</span>
                </a>
              </li>
              <li>
                <a href="#notifications" class="flex items-center py-3 px-4 rounded-xl hover:bg-secondary-50 text-secondary-700 transition-all duration-200 hover:shadow-md">
                  <div class="w-8 h-8 rounded-lg bg-secondary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-bell text-secondary-600"></i>
                  </div>
                  <span>Notifications</span>
                  <span class="ml-auto bg-primary-100 text-primary-800 text-xs font-medium px-2 py-0.5 rounded-full">3</span>
                </a>
              </li>
            </ul>
          </nav>

          <div class="mt-6 pt-6 border-t border-secondary-100">
            <a href="/auth/logout" class="flex items-center py-3 px-4 rounded-xl hover:bg-red-50 text-red-600 transition-all duration-200 hover:shadow-md group">
              <div class="w-8 h-8 rounded-lg bg-red-100 flex items-center justify-center mr-3 group-hover:bg-red-200 transition-colors duration-200">
                <i class="fas fa-sign-out-alt text-red-600"></i>
              </div>
              <span>Logout</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content area -->
    <div class="w-full md:w-3/4">
      <!-- Account information section -->
      <section id="account" class="mb-10" data-aos="fade-up" data-aos-duration="1000">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
            <div class="w-8 h-8 rounded-lg bg-primary-100 flex items-center justify-center mr-3">
              <i class="fas fa-user-circle text-primary-600"></i>
            </div>
            Account Information
          </h2>
          <button class="bg-white py-2 px-4 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
            <i class="fas fa-pencil-alt mr-2 text-primary-500"></i> Edit Profile
          </button>
        </div>

        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div class="space-y-2 bg-secondary-50/50 p-4 rounded-xl">
                <p class="text-secondary-500 text-sm font-medium">Username</p>
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-user text-primary-600"></i>
                  </div>
                  <p class="font-semibold text-secondary-900 text-lg"><%= user.username %></p>
                </div>
              </div>

              <div class="space-y-2 bg-secondary-50/50 p-4 rounded-xl">
                <p class="text-secondary-500 text-sm font-medium">Email</p>
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-envelope text-primary-600"></i>
                  </div>
                  <div>
                    <p class="font-semibold text-secondary-900 text-lg"><%= user.email %></p>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <i class="fas fa-check-circle mr-1"></i> Verified
                    </span>
                  </div>
                </div>
              </div>

              <div class="space-y-2 bg-secondary-50/50 p-4 rounded-xl">
                <p class="text-secondary-500 text-sm font-medium">Full Name</p>
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-id-card text-primary-600"></i>
                  </div>
                  <p class="font-semibold text-secondary-900 text-lg"><%= user.fullName || 'Not provided' %></p>
                </div>
              </div>

              <div class="space-y-2 bg-secondary-50/50 p-4 rounded-xl">
                <p class="text-secondary-500 text-sm font-medium">Member Since</p>
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                    <i class="fas fa-calendar-alt text-primary-600"></i>
                  </div>
                  <p class="font-semibold text-secondary-900 text-lg">
                    <% if (user.createdAt) { %>
                      <%= new Date(user.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) %>
                    <% } else { %>
                      N/A
                    <% } %>
                  </p>
                </div>
              </div>
            </div>

            <div class="mt-8 pt-6 border-t border-secondary-100">
              <h4 class="font-semibold text-secondary-900 mb-4 flex items-center">
                <i class="fas fa-shield-alt text-primary-600 mr-2"></i>
                Account Security
              </h4>
              <div class="bg-secondary-50/50 p-4 rounded-xl flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <i class="fas fa-lock text-blue-600"></i>
                  </div>
                  <div>
                    <p class="font-medium text-secondary-900">Password</p>
                    <p class="text-secondary-500 text-sm">Last changed 3 months ago</p>
                  </div>
                </div>
                <button class="bg-white py-2 px-4 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 hover:bg-primary-50 hover:text-primary-700">
                  <i class="fas fa-key mr-1"></i> Change Password
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Fantasy teams section -->
      <section id="teams" class="mb-10" data-aos="fade-up" data-aos-delay="200" data-aos-duration="1000">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
            <div class="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
              <i class="fas fa-trophy text-blue-600"></i>
            </div>
            My Fantasy Teams
          </h2>
          <a href="/fantasy" class="bg-primary-600 hover:bg-primary-700 py-2 px-4 rounded-xl text-sm font-medium text-white shadow-md hover:shadow-lg transition-all duration-200 flex items-center">
            <i class="fas fa-plus mr-2"></i> Join League
          </a>
        </div>

        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
          <div class="p-8">
            <!-- Teams list would go here -->
            <div class="text-center py-12 px-4">
              <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-trophy text-blue-500 text-3xl"></i>
              </div>
              <h4 class="text-xl font-semibold text-secondary-900 mb-3">No Fantasy Teams Yet</h4>
              <p class="text-secondary-600 mb-8 max-w-md mx-auto">Join a fantasy league to start building your dream team and compete with other sports fans!</p>
              <a href="/fantasy" class="bg-primary-600 hover:bg-primary-700 py-3 px-8 rounded-xl inline-flex items-center justify-center text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 group">
                <span>Browse Leagues</span>
                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-200"></i>
              </a>
            </div>
          </div>
        </div>
      </section>

      <!-- Activity section -->
      <section id="activity" class="mb-10" data-aos="fade-up" data-aos-delay="400" data-aos-duration="1000">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
            <div class="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
              <i class="fas fa-history text-purple-600"></i>
            </div>
            Recent Activity
          </h2>
          <button class="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center hover:underline">
            View All
            <i class="fas fa-chevron-right ml-1 text-xs"></i>
          </button>
        </div>

        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
          <div class="p-8">
            <div class="space-y-6">
              <div class="flex items-start bg-secondary-50/50 p-4 rounded-xl hover:shadow-md transition-all duration-200 cursor-pointer">
                <div class="flex-shrink-0 mr-4">
                  <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <i class="fas fa-sign-in-alt text-green-600 text-lg"></i>
                  </div>
                </div>
                <div>
                  <p class="text-secondary-900 font-medium">You logged in from a new device</p>
                  <p class="text-secondary-500 text-sm flex items-center mt-1">
                    <i class="fas fa-clock mr-1"></i>
                    Today at 10:30 AM
                  </p>
                </div>
                <div class="ml-auto">
                  <button class="text-secondary-400 hover:text-secondary-600">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                </div>
              </div>

              <div class="flex items-start bg-secondary-50/50 p-4 rounded-xl hover:shadow-md transition-all duration-200 cursor-pointer">
                <div class="flex-shrink-0 mr-4">
                  <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <i class="fas fa-user-edit text-blue-600 text-lg"></i>
                  </div>
                </div>
                <div>
                  <p class="text-secondary-900 font-medium">You updated your profile information</p>
                  <p class="text-secondary-500 text-sm flex items-center mt-1">
                    <i class="fas fa-clock mr-1"></i>
                    Yesterday at 2:15 PM
                  </p>
                </div>
                <div class="ml-auto">
                  <button class="text-secondary-400 hover:text-secondary-600">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                </div>
              </div>

              <div class="flex items-start bg-secondary-50/50 p-4 rounded-xl hover:shadow-md transition-all duration-200 cursor-pointer">
                <div class="flex-shrink-0 mr-4">
                  <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                    <i class="fas fa-trophy text-purple-600 text-lg"></i>
                  </div>
                </div>
                <div>
                  <p class="text-secondary-900 font-medium">You viewed the Fantasy Leagues page</p>
                  <p class="text-secondary-500 text-sm flex items-center mt-1">
                    <i class="fas fa-clock mr-1"></i>
                    3 days ago
                  </p>
                </div>
                <div class="ml-auto">
                  <button class="text-secondary-400 hover:text-secondary-600">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add CSS animations dynamically
    const styleSheet = document.createElement("style");
    styleSheet.textContent = `
      @keyframes sectionFocus {
        0% { transform: scale(1); }
        50% { transform: scale(1.01); }
        100% { transform: scale(1); }
      }

      .animate-section-focus {
        animation: sectionFocus 0.5s ease-in-out;
      }

      @keyframes bounceSlight {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-3px); }
      }

      .animate-bounce-subtle {
        animation: bounceSlight 0.5s infinite;
      }
    `;
    document.head.appendChild(styleSheet);

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          // Update active state in navigation
          document.querySelectorAll('nav a').forEach(link => {
            // Remove active state from all links
            link.classList.remove('bg-primary-50', 'text-primary-700');
            link.classList.add('hover:bg-secondary-50', 'text-secondary-700');

            // Reset icon background
            const iconContainer = link.querySelector('div');
            if (iconContainer) {
              iconContainer.classList.remove('bg-primary-100');
              iconContainer.classList.add('bg-secondary-100');

              // Reset icon color
              const icon = iconContainer.querySelector('i');
              if (icon) {
                icon.classList.remove('text-primary-600');
                icon.classList.add('text-secondary-600');
              }
            }
          });

          // Add active state to clicked link
          this.classList.remove('hover:bg-secondary-50', 'text-secondary-700');
          this.classList.add('bg-primary-50', 'text-primary-700');

          // Update icon background
          const iconContainer = this.querySelector('div');
          if (iconContainer) {
            iconContainer.classList.remove('bg-secondary-100');
            iconContainer.classList.add('bg-primary-100');

            // Update icon color
            const icon = iconContainer.querySelector('i');
            if (icon) {
              icon.classList.remove('text-secondary-600');
              icon.classList.add('text-primary-600');
            }
          }

          // Add animation to the target section
          targetElement.classList.add('animate-section-focus');
          setTimeout(() => {
            targetElement.classList.remove('animate-section-focus');
          }, 1000);

          // Scroll to target with offset for header
          window.scrollTo({
            top: targetElement.offsetTop - 100,
            behavior: 'smooth'
          });
        }
      });
    });

    // Add hover effects to activity items
    const activityItems = document.querySelectorAll('#activity .flex.items-start');
    activityItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        const icon = this.querySelector('.w-12.h-12 i');
        if (icon) {
          icon.classList.add('animate-bounce-subtle');
        }
      });

      item.addEventListener('mouseleave', function() {
        const icon = this.querySelector('.w-12.h-12 i');
        if (icon) {
          icon.classList.remove('animate-bounce-subtle');
        }
      });
    });
  });
</script>