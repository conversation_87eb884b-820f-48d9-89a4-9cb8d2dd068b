<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0 opacity-50">
    <div class="absolute -top-24 -left-24 w-96 h-96 bg-primary-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob"></div>
    <div class="absolute top-[30%] -right-24 w-96 h-96 bg-accent-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-24 left-[30%] w-96 h-96 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000"></div>
  </div>

  <div class="max-w-md w-full relative z-10" data-aos="fade-up" data-aos-duration="1000">
    <!-- Logo and title -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 shadow-glow mb-5 transform transition-all duration-500 hover:rotate-12 hover:scale-110">
        <i class="fas fa-lock text-white text-3xl"></i>
      </div>
      <h2 class="text-4xl font-bold font-heading text-secondary-900 mb-2">Reset Password</h2>
      <p class="text-lg text-secondary-600">Enter your new password below</p>
    </div>

    <!-- Card with form -->
    <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-strong border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-strong hover:scale-[1.01]">
      <div class="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-5 px-6 relative overflow-hidden">
        <!-- Decorative pattern -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>

        <h3 class="text-xl font-bold font-heading flex items-center relative z-10">
          <i class="fas fa-key mr-2"></i> Create New Password
        </h3>
      </div>

      <form action="/auth/reset-password" method="POST" class="py-8 px-8">
        <input type="hidden" name="token" value="<%= locals.token %>">

        <% if (locals.error) { %>
          <div class="bg-red-100/80 backdrop-blur-sm border border-red-200 text-red-700 p-4 rounded-xl mb-6 animate-shake">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-red-100 rounded-full p-1">
                <i class="fas fa-exclamation-circle text-red-500"></i>
              </div>
              <p class="ml-3 text-sm font-medium"><%= error %></p>
            </div>
          </div>
        <% } %>

        <div class="mb-6 group">
          <label for="password" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">New Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
            </div>
            <input type="password" id="password" name="password"
                  class="form-input w-full pl-10 pr-10 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 transition-all duration-300"
                  required autocomplete="new-password" minlength="6" placeholder="Enter new password">
            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <p class="text-xs text-secondary-500 mt-1 ml-1">Password must be at least 6 characters long</p>
        </div>

        <div class="mb-6 group">
          <label for="confirmPassword" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Confirm Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200"></i>
            </div>
            <input type="password" id="confirmPassword" name="confirmPassword"
                  class="form-input w-full pl-10 pr-10 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 transition-all duration-300"
                  required autocomplete="new-password" placeholder="Confirm new password">
            <button type="button" id="toggleConfirmPassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
              <i class="fas fa-eye"></i>
            </button>
          </div>
        </div>

        <!-- Password strength indicator -->
        <div class="mb-6">
          <div class="flex items-center justify-between text-xs text-secondary-500 mb-1">
            <span>Password Strength</span>
            <span id="strengthText">Weak</span>
          </div>
          <div class="w-full bg-secondary-200 rounded-full h-2">
            <div id="strengthBar" class="bg-red-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
          </div>
        </div>

        <button type="submit" class="btn-primary w-full py-3 rounded-xl flex items-center justify-center group relative overflow-hidden shadow-lg">
          <span class="relative z-10 font-semibold">Reset Password</span>
          <i class="fas fa-check ml-2 relative z-10 transform group-hover:scale-110 transition-transform duration-200"></i>
          <div class="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 transition-all duration-300 group-hover:scale-105"></div>
        </button>

        <div class="mt-8 text-center text-sm text-secondary-600">
          Remember your password?
          <a href="/auth/login" class="text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200 hover:underline">
            Back to Login
          </a>
        </div>
      </form>
    </div>

    <!-- Security note -->
    <div class="mt-6 text-center text-xs text-secondary-500 flex items-center justify-center bg-white/30 backdrop-blur-sm py-2 px-4 rounded-full shadow-sm">
      <i class="fas fa-shield-alt mr-2 text-primary-500"></i>
      <span>Your password will be securely encrypted</span>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggles
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
    const confirmPassword = document.getElementById('confirmPassword');

    if (togglePassword && password) {
      togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
      });
    }

    if (toggleConfirmPassword && confirmPassword) {
      toggleConfirmPassword.addEventListener('click', function() {
        const type = confirmPassword.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPassword.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
      });
    }

    // Password strength checker
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');

    function checkPasswordStrength(password) {
      let strength = 0;
      let text = 'Weak';
      let color = 'bg-red-500';
      let width = '25%';

      if (password.length >= 6) strength++;
      if (password.match(/[a-z]/)) strength++;
      if (password.match(/[A-Z]/)) strength++;
      if (password.match(/[0-9]/)) strength++;
      if (password.match(/[^a-zA-Z0-9]/)) strength++;

      switch (strength) {
        case 0:
        case 1:
          text = 'Weak';
          color = 'bg-red-500';
          width = '25%';
          break;
        case 2:
          text = 'Fair';
          color = 'bg-yellow-500';
          width = '50%';
          break;
        case 3:
          text = 'Good';
          color = 'bg-blue-500';
          width = '75%';
          break;
        case 4:
        case 5:
          text = 'Strong';
          color = 'bg-green-500';
          width = '100%';
          break;
      }

      strengthBar.className = `h-2 rounded-full transition-all duration-300 ${color}`;
      strengthBar.style.width = width;
      strengthText.textContent = text;
    }

    if (password) {
      password.addEventListener('input', function() {
        checkPasswordStrength(this.value);
      });
    }

    // Password confirmation validation
    function validatePasswordMatch() {
      if (confirmPassword.value && password.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity('Passwords do not match');
        confirmPassword.classList.add('border-red-500');
      } else {
        confirmPassword.setCustomValidity('');
        confirmPassword.classList.remove('border-red-500');
      }
    }

    if (confirmPassword) {
      confirmPassword.addEventListener('input', validatePasswordMatch);
      password.addEventListener('input', validatePasswordMatch);
    }

    // Input focus effects
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentNode.parentNode.classList.add('focused');
        this.classList.add('input-focused');
      });

      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentNode.parentNode.classList.remove('focused');
        }
        this.classList.remove('input-focused');
      });

      if (input.value) {
        input.parentNode.parentNode.classList.add('focused');
      }
    });

    // Background animation
    const animateBackground = () => {
      const blobs = document.querySelectorAll('.animate-blob');
      blobs.forEach(blob => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        blob.style.transform = `translate(${randomX}px, ${randomY}px)`;
        setTimeout(() => {
          blob.style.transition = 'transform 15s ease-in-out';
        }, 100);
      });
    };

    animateBackground();
    setInterval(animateBackground, 15000);
  });
</script>

<style>
  .focused .form-label {
    color: #2563eb;
    transform: translateY(-0.5rem) scale(0.9);
    transform-origin: left top;
  }

  .form-label {
    transition: all 0.2s ease-out;
  }

  .group:focus-within .fas {
    color: #3b82f6;
  }

  .input-focused {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px #dbeafe !important;
  }

  .animate-blob {
    animation: blob-bounce 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  @keyframes blob-bounce {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(20px, -20px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  .animate-shake {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
  }
</style>
