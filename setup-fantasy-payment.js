const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupPaymentSystem() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'online_sports'
    });

    console.log('🚀 Setting up fantasy payment system...');
    
    // Create payment_transactions table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS payment_transactions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        league_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('bank_transfer', 'cash', 'mobile_money', 'credit') DEFAULT 'bank_transfer',
        transaction_reference VARCHAR(100) UNIQUE NOT NULL,
        payment_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
        payment_proof TEXT,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        verified_at TIMESTAMP NULL,
        verified_by INT NULL,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
        
        INDEX idx_user_payment (user_id),
        INDEX idx_league_payment (league_id),
        INDEX idx_payment_status (payment_status),
        INDEX idx_transaction_ref (transaction_reference)
      )
    `);
    console.log('✅ Created payment_transactions table');

    // Create user_league_participation table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS user_league_participation (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        league_id INT NOT NULL,
        payment_transaction_id INT,
        participation_status ENUM('pending_payment', 'paid', 'team_created', 'active') DEFAULT 'pending_payment',
        team_budget DECIMAL(8,2) DEFAULT 100.00,
        budget_used DECIMAL(8,2) DEFAULT 0.00,
        players_selected INT DEFAULT 0,
        max_players INT DEFAULT 11,
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
        FOREIGN KEY (payment_transaction_id) REFERENCES payment_transactions(id) ON DELETE SET NULL,
        
        UNIQUE KEY unique_user_league (user_id, league_id),
        INDEX idx_user_participation (user_id),
        INDEX idx_league_participation (league_id),
        INDEX idx_participation_status (participation_status)
      )
    `);
    console.log('✅ Created user_league_participation table');

    // Create fantasy_team_selections table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS fantasy_team_selections (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        league_id INT NOT NULL,
        player_id INT NOT NULL,
        position VARCHAR(50) NOT NULL,
        is_captain BOOLEAN DEFAULT FALSE,
        is_vice_captain BOOLEAN DEFAULT FALSE,
        player_price DECIMAL(8,2) NOT NULL,
        selection_order INT,
        selected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
        FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_user_league_player (user_id, league_id, player_id),
        INDEX idx_user_selections (user_id, league_id),
        INDEX idx_player_selections (player_id),
        INDEX idx_position_selections (position)
      )
    `);
    console.log('✅ Created fantasy_team_selections table');

    // Create payment_methods table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS payment_methods (
        id INT PRIMARY KEY AUTO_INCREMENT,
        method_name VARCHAR(50) NOT NULL,
        method_type ENUM('bank_transfer', 'cash', 'mobile_money', 'credit') NOT NULL,
        account_details JSON,
        is_active BOOLEAN DEFAULT TRUE,
        display_order INT DEFAULT 0,
        instructions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_method_type (method_type),
        INDEX idx_active_methods (is_active)
      )
    `);
    console.log('✅ Created payment_methods table');

    // Insert default payment methods
    await connection.query(`
      INSERT IGNORE INTO payment_methods (method_name, method_type, account_details, instructions, display_order) VALUES
      ('Bank Transfer', 'bank_transfer', '{"bank_name": "Sports Bank", "account_number": "**********", "account_name": "Online Sports Website", "routing_number": "123456"}', 'Transfer the league entry fee to our bank account and upload the receipt.', 1),
      ('Cash Payment', 'cash', '{"contact_person": "Sports Admin", "phone": "+**********", "address": "123 Sports Street, City"}', 'Contact our admin to arrange cash payment at our office.', 2),
      ('Mobile Money', 'mobile_money', '{"service": "SportsPay", "number": "+**********", "name": "Sports Admin"}', 'Send payment via mobile money and provide transaction ID.', 3),
      ('Credit System', 'credit', '{"description": "Internal credit system"}', 'Use your account credits to pay for league entry.', 4)
    `);
    console.log('✅ Inserted default payment methods');

    // Update fantasy_leagues to have entry_fee
    await connection.query(`
      ALTER TABLE fantasy_leagues 
      ADD COLUMN IF NOT EXISTS entry_fee DECIMAL(8,2) DEFAULT 25.00,
      ADD COLUMN IF NOT EXISTS prize_pool DECIMAL(10,2) DEFAULT 500.00
    `);
    console.log('✅ Updated fantasy_leagues table');

    console.log('🎉 Fantasy payment system setup complete!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('1. Restart your server: npm start');
    console.log('2. Go to /fantasy to see the new payment system');
    console.log('3. Try joining a league to test the payment flow');
    
  } catch (error) {
    console.error('❌ Error setting up payment system:', error.message);
  } finally {
    if (connection) await connection.end();
  }
}

setupPaymentSystem();
