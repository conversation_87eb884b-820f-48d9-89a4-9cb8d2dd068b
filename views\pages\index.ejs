<!-- Hero section -->
<section class="relative rounded-lg shadow-md mb-8 overflow-hidden">
  <!-- Background image -->
  <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('/images/sport.jpg'); filter: brightness(0.7);">
  </div>

  <!-- Gradient overlay -->
  <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-700/70"></div>

  <!-- Content -->
  <div class="relative py-16 md:py-24 container mx-auto px-4 text-center text-white">
    <h1 class="text-4xl md:text-6xl font-bold mb-4 drop-shadow-lg">Welcome to SportZone</h1>
    <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">Your ultimate destination for sports news, live streaming, and fantasy leagues</p>
    <div class="flex flex-wrap justify-center gap-4">
      <a href="/news" class="bg-white text-blue-800 hover:bg-blue-100 font-bold py-3 px-8 rounded-full transition duration-300 transform hover:scale-105 shadow-lg">
        <i class="fas fa-newspaper mr-2"></i> Latest News
      </a>
      <a href="/streaming" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full transition duration-300 transform hover:scale-105 shadow-lg">
        <i class="fas fa-play-circle mr-2"></i> Watch Live
      </a>
      <a href="/fantasy" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-full transition duration-300 transform hover:scale-105 shadow-lg">
        <i class="fas fa-trophy mr-2"></i> Fantasy Leagues
      </a>
    </div>
  </div>
</section>

<!-- Sports categories -->
<section class="mb-12">
  <h2 class="text-2xl font-bold mb-6 text-gray-800">Popular Sports</h2>
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
    <% if (locals.categories && categories.length > 0) { %>
      <% categories.forEach(category => { %>
        <a href="/news?category=<%= category.id %>" class="sports-card text-center">
          <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-<%= category.name.toLowerCase() === 'football' ? 'futbol' :
                              category.name.toLowerCase() === 'basketball' ? 'basketball-ball' :
                              category.name.toLowerCase() === 'cricket' ? 'baseball' :
                              category.name.toLowerCase() === 'tennis' ? 'table-tennis' :
                              category.name.toLowerCase() === 'formula 1' ? 'flag-checkered' : 'running' %> text-blue-600 text-2xl"></i>
          </div>
          <h3 class="font-semibold text-gray-800"><%= category.name %></h3>
        </a>
      <% }) %>
    <% } else { %>
      <div class="col-span-5 text-center py-8 text-gray-500">
        No sports categories found
      </div>
    <% } %>
  </div>
</section>

<!-- Latest news -->
<section class="mb-12">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold text-gray-800">Latest News</h2>
    <a href="/news" class="text-blue-600 hover:text-blue-800 font-semibold">View All</a>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <% if (locals.latestNews && latestNews.length > 0) { %>
      <% latestNews.forEach(article => { %>
        <div class="news-card">
          <img src="<%= article.image || '/images/sport.jpg' %>"
               alt="<%= article.title %>"
               class="news-card-image">
          <div class="news-card-content">
            <div class="flex items-center mb-2">
              <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                <%= article.category_name || 'General' %>
              </span>
              <span class="text-gray-500 text-sm ml-2">
                <%= new Date(article.published_at).toLocaleDateString() %>
              </span>
            </div>
            <h3 class="font-bold text-xl mb-2 text-gray-800"><%= article.title %></h3>
            <p class="text-gray-600 mb-4">
              <%= article.content.substring(0, 100) %>...
            </p>
            <a href="/news/<%= article.id %>" class="text-blue-600 hover:text-blue-800 font-semibold">
              Read More
            </a>
          </div>
        </div>
      <% }) %>
    <% } else { %>
      <div class="col-span-3 text-center py-8 text-gray-500">
        No news articles found
      </div>
    <% } %>
  </div>
</section>

<!-- Live Games -->
<section class="mb-12">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center">
      <h2 class="text-2xl font-bold text-gray-800 mr-3">Live Now</h2>
      <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full animate-pulse flex items-center">
        <span class="w-2 h-2 bg-white rounded-full mr-1"></span> LIVE
      </span>
    </div>
    <a href="/streaming" class="text-blue-600 hover:text-blue-800 font-semibold">View All</a>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <%
      // Filter for live games
      const liveGames = locals.upcomingGames ? upcomingGames.filter(game => game.status === 'live') : [];
    %>

    <% if (liveGames && liveGames.length > 0) { %>
      <% liveGames.slice(0, 3).forEach(game => { %>
        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
          <div class="relative">
            <div class="absolute top-0 left-0 bg-red-600 text-white text-xs font-bold px-2 py-1 m-2 rounded-md">
              LIVE
            </div>
            <div class="bg-gray-200 h-40 flex items-center justify-center">
              <div class="text-center p-4">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                      <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                    </div>
                    <span class="font-semibold"><%= game.home_team_name %></span>
                  </div>

                  <div class="mx-4">
                    <div class="text-2xl font-bold">
                      <span class="text-3xl"><%= game.score_home || 0 %></span>
                      <span class="text-gray-400 mx-2">-</span>
                      <span class="text-3xl"><%= game.score_away || 0 %></span>
                    </div>
                  </div>

                  <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-2">
                      <i class="fas fa-shield-alt text-red-600 text-2xl"></i>
                    </div>
                    <span class="font-semibold"><%= game.away_team_name %></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="p-4">
            <div class="flex justify-between items-center mb-2">
              <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                <%= game.sport_name %>
              </span>
              <span class="text-gray-500 text-sm">
                <i class="fas fa-map-marker-alt mr-1"></i> <%= game.venue || 'TBA' %>
              </span>
            </div>

            <a href="/streaming/<%= game.id %>" class="block w-full bg-red-600 hover:bg-red-700 text-white text-center font-bold py-2 px-4 rounded transition duration-300">
              <i class="fas fa-play-circle mr-1"></i> Watch Now
            </a>
          </div>
        </div>
      <% }) %>
    <% } else { %>
      <div class="col-span-3 text-center py-8 text-gray-500 bg-white rounded-lg shadow-md">
        <p class="mb-4">No live games at the moment.</p>
        <p>Check back later or browse upcoming games below.</p>
      </div>
    <% } %>
  </div>
</section>

<!-- Upcoming games -->
<section class="mb-12">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-2xl font-bold text-gray-800">Upcoming Games</h2>
    <a href="/streaming" class="text-blue-600 hover:text-blue-800 font-semibold">View All</a>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <%
      // Filter for scheduled games
      const scheduledGames = locals.upcomingGames ? upcomingGames.filter(game => game.status === 'scheduled') : [];
    %>

    <% if (scheduledGames && scheduledGames.length > 0) { %>
      <div class="divide-y divide-gray-200">
        <% scheduledGames.slice(0, 5).forEach(game => { %>
          <div class="p-4 hover:bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                  <%= game.sport_name %>
                </span>
                <span class="text-gray-500 text-sm">
                  <%= new Date(game.game_time).toLocaleString() %>
                </span>
              </div>
              <a href="/streaming/<%= game.id %>" class="text-blue-600 hover:text-blue-800 text-sm font-semibold">
                <i class="far fa-bell mr-1"></i> Set Reminder
              </a>
            </div>

            <div class="flex items-center justify-between mt-3">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-shield-alt text-blue-600"></i>
                </div>
                <span class="font-semibold"><%= game.home_team_name %></span>
              </div>

              <div class="text-center">
                <span class="text-gray-500 font-semibold">VS</span>
              </div>

              <div class="flex items-center space-x-3">
                <span class="font-semibold"><%= game.away_team_name %></span>
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-shield-alt text-red-600"></i>
                </div>
              </div>
            </div>

            <div class="mt-2 text-sm text-gray-500">
              <i class="fas fa-map-marker-alt mr-1"></i> <%= game.venue || 'TBA' %>
            </div>
          </div>
        <% }) %>
      </div>
    <% } else { %>
      <div class="text-center py-8 text-gray-500">
        No upcoming games found
      </div>
    <% } %>
  </div>
</section>

<!-- Fantasy leagues promo -->
<section class="bg-gradient-to-r from-blue-700 to-blue-900 text-white rounded-lg shadow-md p-8 mb-12">
  <div class="md:flex items-center justify-between">
    <div class="mb-6 md:mb-0 md:w-2/3">
      <h2 class="text-3xl font-bold mb-4">Join Our Fantasy Leagues</h2>
      <p class="text-blue-100 mb-4">
        Create your dream team, compete with friends, and win exciting prizes!
        Our fantasy leagues cover all major sports with real-time scoring and statistics.
      </p>
      <a href="/fantasy" class="inline-block bg-white text-blue-800 hover:bg-blue-100 font-bold py-2 px-6 rounded-full">
        Get Started
      </a>
    </div>
    <div class="md:w-1/3 text-center">
      <i class="fas fa-trophy text-8xl text-yellow-400"></i>
    </div>
  </div>
</section>
