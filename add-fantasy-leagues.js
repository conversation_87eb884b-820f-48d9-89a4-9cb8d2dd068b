const mysql = require('mysql2/promise');
require('dotenv').config();

async function addFantasyLeagues() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'online_sports'
    });

    console.log('🏆 Adding sample fantasy leagues...');

    // First, let's check if we have sports categories
    const [categories] = await connection.query('SELECT * FROM sports_categories LIMIT 5');
    console.log(`Found ${categories.length} sports categories`);

    if (categories.length === 0) {
      console.log('❌ No sports categories found. Please run the sample data import first.');
      return;
    }

    // Add sample fantasy leagues
    const leagues = [
      {
        name: 'Premier Cricket Championship',
        description: 'Join the ultimate cricket fantasy league with top international players!',
        category_id: categories.find(c => c.name.toLowerCase().includes('cricket'))?.id || categories[0].id,
        entry_fee: 25.00,
        prize_pool: 1000.00,
        max_teams: 50,
        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 1 month from now
        status: 'upcoming'
      },
      {
        name: 'Football Fantasy League',
        description: 'Build your dream football team and compete for amazing prizes!',
        category_id: categories.find(c => c.name.toLowerCase().includes('football'))?.id || categories[0].id,
        entry_fee: 30.00,
        prize_pool: 1500.00,
        max_teams: 100,
        start_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        end_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        status: 'upcoming'
      },
      {
        name: 'Basketball Pro League',
        description: 'Draft the best basketball players and dominate the court!',
        category_id: categories.find(c => c.name.toLowerCase().includes('basketball'))?.id || categories[0].id,
        entry_fee: 20.00,
        prize_pool: 800.00,
        max_teams: 30,
        start_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        status: 'upcoming'
      },
      {
        name: 'Quick Cricket Challenge',
        description: 'Fast-paced cricket fantasy league - perfect for beginners!',
        category_id: categories.find(c => c.name.toLowerCase().includes('cricket'))?.id || categories[0].id,
        entry_fee: 15.00,
        prize_pool: 500.00,
        max_teams: 25,
        start_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
        status: 'upcoming'
      },
      {
        name: 'Elite Sports Championship',
        description: 'Multi-sport fantasy league for the ultimate sports fan!',
        category_id: categories[0].id, // Use first available category
        entry_fee: 50.00,
        prize_pool: 2500.00,
        max_teams: 20,
        start_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
        end_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        status: 'upcoming'
      }
    ];

    // Insert leagues
    for (const league of leagues) {
      try {
        const [result] = await connection.query(`
          INSERT INTO fantasy_leagues (name, description, category_id, entry_fee, prize_pool, max_teams, start_date, end_date, status, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `, [
          league.name,
          league.description,
          league.category_id,
          league.entry_fee,
          league.prize_pool,
          league.max_teams,
          league.start_date,
          league.end_date,
          league.status
        ]);
        
        console.log(`✅ Created league: ${league.name} (ID: ${result.insertId})`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ League "${league.name}" already exists, skipping...`);
        } else {
          console.log(`❌ Error creating league "${league.name}":`, error.message);
        }
      }
    }

    // Also make sure players have prices for the fantasy system
    console.log('💰 Setting up player prices...');
    
    await connection.query(`
      UPDATE players 
      SET price = CASE 
        WHEN price IS NULL OR price = 0 THEN 
          ROUND(5 + (RAND() * 15), 2)  -- Random price between $5-20
        ELSE price 
      END,
      is_available = TRUE
      WHERE is_available IS NULL OR is_available = TRUE
    `);
    
    console.log('✅ Updated player prices and availability');

    // Check final results
    const [finalLeagues] = await connection.query(`
      SELECT fl.*, sc.name as category_name 
      FROM fantasy_leagues fl 
      LEFT JOIN sports_categories sc ON fl.category_id = sc.id 
      ORDER BY fl.created_at DESC
    `);
    
    console.log('🎉 Fantasy leagues setup complete!');
    console.log(`📊 Total leagues available: ${finalLeagues.length}`);
    console.log('');
    console.log('📋 Available Leagues:');
    finalLeagues.forEach(league => {
      console.log(`  • ${league.name} (${league.category_name}) - $${league.entry_fee} entry`);
    });
    
    console.log('');
    console.log('🚀 Next steps:');
    console.log('1. Restart your server if it\'s running');
    console.log('2. Go to http://192.168.45.125:3000/fantasy');
    console.log('3. You should now see the fantasy leagues!');

  } catch (error) {
    console.error('❌ Error setting up fantasy leagues:', error.message);
  } finally {
    if (connection) await connection.end();
  }
}

addFantasyLeagues();
