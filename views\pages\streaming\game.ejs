<div class="mb-6">
  <div class="flex justify-between items-center mb-4">
    <a href="/streaming" class="text-blue-600 hover:text-blue-800">
      <i class="fas fa-arrow-left mr-1"></i> Back to Streams
    </a>
    <div class="flex items-center">
      <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded mr-2">
        <%= game.sport_name %>
      </span>
      <% if (game.status === 'live') { %>
        <span class="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded flex items-center">
          <span class="w-2 h-2 bg-red-600 rounded-full mr-1 animate-pulse"></span> LIVE
        </span>
      <% } else { %>
        <span class="bg-gray-100 text-gray-800 text-xs font-semibold px-2 py-1 rounded">
          <%= game.status.toUpperCase() %>
        </span>
      <% } %>
    </div>
  </div>

  <h1 class="text-3xl font-bold text-gray-800 mb-2">
    <%= game.home_team_name %> vs <%= game.away_team_name %>
  </h1>

  <div class="text-gray-600 mb-6">
    <span class="mr-4">
      <i class="far fa-calendar-alt mr-1"></i>
      <span class="format-date" data-date="<%= game.game_time %>">
        <%= new Date(game.game_time).toLocaleDateString() %>
      </span>
    </span>
    <span class="mr-4">
      <i class="far fa-clock mr-1"></i>
      <span class="format-time" data-time="<%= game.game_time %>">
        <%= new Date(game.game_time).toLocaleTimeString() %>
      </span>
    </span>
    <span>
      <i class="fas fa-map-marker-alt mr-1"></i> <%= game.venue || 'TBA' %>
    </span>
  </div>
</div>

<!-- Video player section -->
<div class="bg-black rounded-lg overflow-hidden mb-8">
  <% if (game.stream_url) { %>
    <div class="aspect-w-16 aspect-h-9">
      <iframe src="<%= game.stream_url %>"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen
              class="w-full h-full"></iframe>
    </div>
  <% } else { %>
    <div class="aspect-w-16 aspect-h-9 bg-gray-900 flex items-center justify-center">
      <div class="text-center text-gray-400">
        <i class="fas fa-video-slash text-5xl mb-4"></i>
        <p class="text-xl">Stream not available</p>
        <p class="text-sm mt-2">
          <% if (game.status === 'scheduled') { %>
            The stream will be available when the game starts.
          <% } else { %>
            Please check back later.
          <% } %>
        </p>
      </div>
    </div>
  <% } %>
</div>

<!-- Game info section -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
  <!-- Score and teams -->
  <div class="lg:col-span-2">
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="text-center">
          <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-shield-alt text-blue-600 text-3xl"></i>
          </div>
          <h3 class="font-bold text-lg"><%= game.home_team_name %></h3>
          <p class="text-gray-500">Home</p>
        </div>

        <div class="text-center">
          <div class="text-3xl font-bold mb-2">
            <span class="text-5xl"><%= game.score_home || 0 %></span>
            <span class="text-gray-400 mx-2">-</span>
            <span class="text-5xl"><%= game.score_away || 0 %></span>
          </div>
          <% if (game.status === 'live') { %>
            <span class="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded">
              LIVE
            </span>
          <% } else if (game.status === 'completed') { %>
            <span class="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded">
              FINAL
            </span>
          <% } else { %>
            <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
              <%= game.status.toUpperCase() %>
            </span>
          <% } %>
        </div>

        <div class="text-center">
          <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-shield-alt text-red-600 text-3xl"></i>
          </div>
          <h3 class="font-bold text-lg"><%= game.away_team_name %></h3>
          <p class="text-gray-500">Away</p>
        </div>
      </div>

      <!-- Team stats (placeholder) -->
      <div class="grid grid-cols-3 gap-4 text-center">
        <div>
          <p class="text-gray-500 text-sm">Possession</p>
          <div class="h-2 bg-gray-200 rounded-full mt-1 mb-2">
            <div class="h-2 bg-blue-600 rounded-full" style="width: 55%"></div>
          </div>
          <div class="flex justify-between text-xs">
            <span>55%</span>
            <span>45%</span>
          </div>
        </div>

        <div>
          <p class="text-gray-500 text-sm">Shots</p>
          <div class="h-2 bg-gray-200 rounded-full mt-1 mb-2">
            <div class="h-2 bg-blue-600 rounded-full" style="width: 60%"></div>
          </div>
          <div class="flex justify-between text-xs">
            <span>12</span>
            <span>8</span>
          </div>
        </div>

        <div>
          <p class="text-gray-500 text-sm">Fouls</p>
          <div class="h-2 bg-gray-200 rounded-full mt-1 mb-2">
            <div class="h-2 bg-blue-600 rounded-full" style="width: 40%"></div>
          </div>
          <div class="flex justify-between text-xs">
            <span>4</span>
            <span>6</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Live chat (placeholder) -->
  <div>
    <div class="bg-white rounded-lg shadow-md h-full">
      <div class="border-b p-4">
        <h3 class="font-bold text-lg">Live Chat</h3>
      </div>

      <div class="p-4 h-64 overflow-y-auto">
        <div class="text-center text-gray-500 py-8">
          <p>Please login to participate in the live chat.</p>
        </div>
      </div>

      <div class="border-t p-4">
        <div class="flex">
          <input type="text" placeholder="Type a message..."
                 class="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                 disabled>
          <button class="bg-blue-600 text-white px-4 py-2 rounded-r-md" disabled>
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Team lineups -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
  <!-- Home team -->
  <div>
    <h3 class="text-xl font-bold text-gray-800 mb-4"><%= game.home_team_name %> Lineup</h3>
    <div class="bg-white rounded-lg shadow-md p-4">
      <% if (locals.homePlayers && homePlayers.length > 0) { %>
        <div class="space-y-2">
          <% homePlayers.forEach(player => { %>
            <div class="flex items-center p-2 border-b last:border-0">
              <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                <i class="fas fa-user text-gray-400"></i>
              </div>
              <div>
                <h4 class="font-semibold"><%= player.name %></h4>
                <div class="text-sm text-gray-500">
                  <% if (player.position) { %>
                    <span class="mr-2"><%= player.position %></span>
                  <% } %>
                  <% if (player.jersey_number) { %>
                    <span>Jersey #<%= player.jersey_number %></span>
                  <% } %>
                </div>
              </div>
            </div>
          <% }) %>
        </div>
      <% } else { %>
        <div class="text-center py-8 text-gray-500">
          <p>No player information available</p>
        </div>
      <% } %>
    </div>
  </div>

  <!-- Away team -->
  <div>
    <h3 class="text-xl font-bold text-gray-800 mb-4"><%= game.away_team_name %> Lineup</h3>
    <div class="bg-white rounded-lg shadow-md p-4">
      <% if (locals.awayPlayers && awayPlayers.length > 0) { %>
        <div class="space-y-2">
          <% awayPlayers.forEach(player => { %>
            <div class="flex items-center p-2 border-b last:border-0">
              <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                <i class="fas fa-user text-gray-400"></i>
              </div>
              <div>
                <h4 class="font-semibold"><%= player.name %></h4>
                <div class="text-sm text-gray-500">
                  <% if (player.position) { %>
                    <span class="mr-2"><%= player.position %></span>
                  <% } %>
                  <% if (player.jersey_number) { %>
                    <span>Jersey #<%= player.jersey_number %></span>
                  <% } %>
                </div>
              </div>
            </div>
          <% }) %>
        </div>
      <% } else { %>
        <div class="text-center py-8 text-gray-500">
          <p>No player information available</p>
        </div>
      <% } %>
    </div>
  </div>
</div>
