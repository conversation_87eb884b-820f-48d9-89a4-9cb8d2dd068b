// Script to automatically update your IP address in .env file
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

function getLocalIP() {
  return new Promise((resolve, reject) => {
    exec('ipconfig', (error, stdout, stderr) => {
      if (error) {
        reject(error);
        return;
      }
      
      // Extract IPv4 address from ipconfig output
      const lines = stdout.split('\n');
      for (let line of lines) {
        if (line.includes('IPv4 Address') && !line.includes('127.0.0.1')) {
          const match = line.match(/(\d+\.\d+\.\d+\.\d+)/);
          if (match) {
            resolve(match[1]);
            return;
          }
        }
      }
      reject(new Error('Could not find IPv4 address'));
    });
  });
}

async function updateEnvFile() {
  try {
    const ip = await getLocalIP();
    const envPath = path.join(__dirname, '.env');
    
    if (!fs.existsSync(envPath)) {
      console.error('❌ .env file not found');
      return;
    }
    
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update the WEBSITE_URL with new IP
    const newUrl = `http://${ip}:3000`;
    envContent = envContent.replace(
      /WEBSITE_URL=http:\/\/\d+\.\d+\.\d+\.\d+:3000/,
      `WEBSITE_URL=${newUrl}`
    );
    
    // Update the IP comment
    envContent = envContent.replace(
      /# Your current IP address: \d+\.\d+\.\d+\.\d+/,
      `# Your current IP address: ${ip}`
    );
    
    fs.writeFileSync(envPath, envContent);
    
    console.log('✅ IP address updated successfully!');
    console.log(`🌐 New website URL: ${newUrl}`);
    console.log(`📧 Password reset emails will now use: ${newUrl}`);
    console.log('');
    console.log('🔄 Please restart your server to apply changes:');
    console.log('   npm start');
    
  } catch (error) {
    console.error('❌ Error updating IP address:', error.message);
  }
}

updateEnvFile();
