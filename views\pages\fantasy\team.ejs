<!-- Team header with background -->
<div class="relative mb-12" data-aos="fade-up" data-aos-duration="1000">
  <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl opacity-10"></div>
  <div class="relative py-8 px-6 rounded-2xl overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute top-0 right-0 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
    <div class="absolute -bottom-8 -left-8 w-64 h-64 bg-blue-700 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>

    <div class="relative">
      <div class="flex flex-col md:flex-row md:items-center justify-between gap-6">
        <div>
          <div class="flex items-center mb-4">
            <a href="/fantasy" class="bg-white/80 backdrop-blur-sm py-2 px-4 rounded-xl text-sm font-medium text-blue-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center mr-4 group">
              <i class="fas fa-arrow-left mr-2 transform group-hover:-translate-x-1 transition-transform duration-200"></i>
              Back to Leagues
            </a>

            <% if (isOwner) { %>
              <button class="bg-blue-600/80 backdrop-blur-sm hover:bg-blue-700 py-2 px-4 rounded-xl text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
                <i class="fas fa-pencil-alt mr-2"></i> Edit Team
              </button>
            <% } %>
          </div>

          <h1 class="text-4xl font-bold font-heading text-secondary-900 mb-2 flex items-center">
            <div class="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center mr-3 shadow-md">
              <i class="fas fa-trophy text-blue-600 text-xl"></i>
            </div>
            <%= team.name %>
          </h1>
          <p class="text-secondary-600 max-w-2xl flex items-center">
            <span class="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded-full mr-3">
              <%= team.sport_name %>
            </span>
            <span class="text-secondary-500">
              <i class="fas fa-users mr-1"></i> League: <span class="font-medium text-secondary-700"><%= team.league_name %></span>
            </span>
          </p>
        </div>

        <div class="flex space-x-4">
          <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-md p-4 flex items-center">
            <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
              <i class="fas fa-star text-yellow-500"></i>
            </div>
            <div>
              <p class="text-xs text-secondary-500">Points</p>
              <p class="text-xl font-bold text-secondary-900"><%= team.points %></p>
            </div>
          </div>

          <% if (team.team_rank) { %>
            <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-md p-4 flex items-center">
              <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <i class="fas fa-medal text-blue-600"></i>
              </div>
              <div>
                <p class="text-xs text-secondary-500">Rank</p>
                <p class="text-xl font-bold text-secondary-900">#<%= team.team_rank %></p>
              </div>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
  <!-- Team roster section -->
  <div class="lg:col-span-2">
    <section id="team-roster" class="mb-8" data-aos="fade-up" data-aos-duration="1000">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
          <div class="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
            <i class="fas fa-users text-blue-600"></i>
          </div>
          Team Roster
        </h2>
        <div class="flex items-center">
          <span class="text-sm text-secondary-500 mr-3">
            <i class="fas fa-users mr-1"></i> <%= locals.teamPlayers ? teamPlayers.length : 0 %> Players
          </span>
          <% if (isOwner) { %>
            <button class="bg-white py-2 px-4 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
              <i class="fas fa-sort mr-2 text-blue-500"></i> Sort Players
            </button>
          <% } %>
        </div>
      </div>

      <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div class="p-6">
          <% if (locals.teamPlayers && teamPlayers.length > 0) { %>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <% teamPlayers.forEach(player => { %>
                <div class="player-card bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.01] overflow-hidden">
                  <div class="p-4 flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 border-2 border-blue-50">
                        <i class="fas fa-user text-blue-500"></i>
                      </div>
                      <div>
                        <h3 class="font-semibold text-secondary-900"><%= player.name %></h3>
                        <div class="flex items-center mt-1">
                          <% if (player.position) { %>
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full mr-2">
                              <%= player.position %>
                            </span>
                          <% } %>
                          <% if (player.jersey_number) { %>
                            <span class="text-xs text-secondary-500">
                              <i class="fas fa-tshirt mr-1"></i> #<%= player.jersey_number %>
                            </span>
                          <% } %>
                        </div>
                      </div>
                    </div>

                    <% if (isOwner) { %>
                      <button onclick="removePlayerFromTeam(<%= team.id %>, <%= player.roster_id %>)"
                              class="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center text-red-500 hover:bg-red-100 hover:text-red-600 transition-all duration-200">
                        <i class="fas fa-times"></i>
                      </button>
                    <% } %>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } else { %>
            <div class="text-center py-12 px-4">
              <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <i class="fas fa-users text-blue-500 text-3xl"></i>
              </div>
              <h4 class="text-xl font-semibold text-secondary-900 mb-3">Your Team Roster is Empty</h4>
              <p class="text-secondary-600 mb-8 max-w-md mx-auto">Add players from the available players list to build your dream team!</p>
              <a href="#available-players" class="bg-blue-600 hover:bg-blue-700 py-3 px-8 rounded-xl inline-flex items-center justify-center text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 group">
                <span>Browse Players</span>
                <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-200"></i>
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </section>
  </div>

  <!-- Available players section -->
  <div>
    <% if (isOwner) { %>
      <section id="available-players" data-aos="fade-up" data-aos-delay="200" data-aos-duration="1000">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold font-heading text-secondary-900 flex items-center">
            <div class="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
              <i class="fas fa-search text-blue-600"></i>
            </div>
            Available Players
          </h2>
        </div>

        <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
          <div class="p-6">
            <div class="mb-4 relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-secondary-400"></i>
              </div>
              <input type="text" id="playerSearch" placeholder="Search players by name..."
                     class="w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-300">
            </div>

            <% if (locals.availablePlayers && availablePlayers.length > 0) { %>
              <div class="space-y-3 max-h-[500px] overflow-y-auto pr-2 custom-scrollbar" id="availablePlayersList">
                <% availablePlayers.forEach(player => { %>
                  <div class="player-card player-item bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-3 flex items-center justify-between" data-name="<%= player.name.toLowerCase() %>">
                    <div class="flex items-center">
                      <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 border-2 border-blue-50">
                        <i class="fas fa-user text-blue-500"></i>
                      </div>
                      <div>
                        <h3 class="font-semibold text-secondary-900"><%= player.name %></h3>
                        <div class="flex items-center mt-1">
                          <span class="text-xs text-secondary-500 mr-2">
                            <i class="fas fa-shield-alt mr-1"></i> <%= player.team_name %>
                          </span>
                          <% if (player.position) { %>
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                              <%= player.position %>
                            </span>
                          <% } %>
                        </div>
                      </div>
                    </div>

                    <button onclick="addPlayerToTeam(<%= team.id %>, <%= player.id %>)"
                            class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center text-green-500 hover:bg-green-100 hover:text-green-600 transition-all duration-200">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                <% }) %>
              </div>
            <% } else { %>
              <div class="text-center py-10 px-4">
                <div class="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <i class="fas fa-search text-blue-500 text-2xl"></i>
                </div>
                <h4 class="text-lg font-semibold text-secondary-900 mb-2">No Available Players</h4>
                <p class="text-secondary-600 mb-4">There are no more players available to add to your team.</p>
              </div>
            <% } %>
          </div>
        </div>
      </section>
    <% } %>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Enhanced player search functionality
    const searchInput = document.getElementById('playerSearch');
    if (searchInput) {
      const playerItems = document.querySelectorAll('.player-item');
      const noResultsMessage = document.createElement('div');
      noResultsMessage.className = 'text-center py-6 text-secondary-500 hidden';
      noResultsMessage.innerHTML = `
        <div class="w-12 h-12 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-3">
          <i class="fas fa-search text-blue-400"></i>
        </div>
        <p>No players match your search</p>
      `;

      // Add no results message to the list
      const playersList = document.getElementById('availablePlayersList');
      if (playersList) {
        playersList.appendChild(noResultsMessage);
      }

      // Search functionality with debounce
      let debounceTimeout;
      searchInput.addEventListener('input', function() {
        clearTimeout(debounceTimeout);

        debounceTimeout = setTimeout(() => {
          const searchTerm = this.value.toLowerCase();
          let matchCount = 0;

          playerItems.forEach(item => {
            const playerName = item.getAttribute('data-name');
            if (playerName.includes(searchTerm)) {
              item.style.display = 'flex';
              // Add animation
              item.classList.add('animate-fade-in');
              setTimeout(() => {
                item.classList.remove('animate-fade-in');
              }, 300);
              matchCount++;
            } else {
              item.style.display = 'none';
            }
          });

          // Show/hide no results message
          if (matchCount === 0 && searchTerm.length > 0) {
            noResultsMessage.classList.remove('hidden');
          } else {
            noResultsMessage.classList.add('hidden');
          }
        }, 300);
      });

      // Clear search button
      const searchContainer = searchInput.parentNode;
      const clearButton = document.createElement('button');
      clearButton.className = 'absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 transition-colors duration-200 hidden';
      clearButton.innerHTML = '<i class="fas fa-times"></i>';
      searchContainer.appendChild(clearButton);

      searchInput.addEventListener('input', function() {
        if (this.value.length > 0) {
          clearButton.classList.remove('hidden');
        } else {
          clearButton.classList.add('hidden');
        }
      });

      clearButton.addEventListener('click', function() {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
        this.classList.add('hidden');
        searchInput.focus();
      });
    }

    // Add hover effects to player cards
    const playerCards = document.querySelectorAll('.player-card');
    playerCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        const icon = this.querySelector('.w-8.h-8 i');
        if (icon) {
          icon.classList.add('animate-bounce-subtle');
        }
      });

      card.addEventListener('mouseleave', function() {
        const icon = this.querySelector('.w-8.h-8 i');
        if (icon) {
          icon.classList.remove('animate-bounce-subtle');
        }
      });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          // Add animation to the target section
          targetElement.classList.add('animate-section-focus');
          setTimeout(() => {
            targetElement.classList.remove('animate-section-focus');
          }, 1000);

          // Scroll to target with offset for header
          window.scrollTo({
            top: targetElement.offsetTop - 100,
            behavior: 'smooth'
          });
        }
      });
    });

    // Subtle background animation for blobs
    const animateBackground = () => {
      const blobs = document.querySelectorAll('.animate-blob');
      blobs.forEach(blob => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        blob.style.transform = `translate(${randomX}px, ${randomY}px)`;
        setTimeout(() => {
          blob.style.transition = 'transform 15s ease-in-out';
        }, 100);
      });
    };

    // Run animation every 15 seconds
    animateBackground();
    setInterval(animateBackground, 15000);
  });

  // Enhanced add player function with toast notification
  function addPlayerToTeam(teamId, playerId) {
    // Create loading overlay for the button
    const button = event.currentTarget;
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/fantasy/teams/${teamId}/players`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ playerId }),
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success toast
        showToast('Player added to your team!', 'success');

        // Animate the player card out
        const playerCard = button.closest('.player-card');
        playerCard.classList.add('animate-slide-out-right');

        // Reload after animation
        setTimeout(() => {
          window.location.reload();
        }, 500);
      } else {
        // Reset button
        button.innerHTML = originalContent;
        button.disabled = false;

        // Show error toast
        showToast(data.error || 'Failed to add player to team', 'error');
      }
    })
    .catch(error => {
      console.error('Error adding player to team:', error);

      // Reset button
      button.innerHTML = originalContent;
      button.disabled = false;

      // Show error toast
      showToast('An error occurred. Please try again.', 'error');
    });
  }

  // Enhanced remove player function with confirmation modal
  function removePlayerFromTeam(teamId, rosterId) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in';

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'bg-white rounded-2xl shadow-xl max-w-md w-full mx-4 animate-slide-up';

    modal.innerHTML = `
      <div class="bg-gradient-to-r from-red-600 to-red-700 text-white py-4 px-6 rounded-t-2xl relative">
        <div class="absolute inset-0 opacity-10 rounded-t-2xl">
          <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>
        <h3 class="text-xl font-bold font-heading relative z-10">Remove Player</h3>
      </div>
      <div class="p-6">
        <p class="text-secondary-600 mb-6">Are you sure you want to remove this player from your team?</p>
        <div class="flex space-x-3 justify-end">
          <button id="cancelBtn" class="bg-white border border-secondary-200 hover:bg-secondary-50 py-2.5 px-5 rounded-xl text-sm font-medium text-secondary-700 shadow-sm hover:shadow-md transition-all duration-200">
            Cancel
          </button>
          <button id="confirmBtn" class="bg-red-600 hover:bg-red-700 py-2.5 px-5 rounded-xl text-sm font-medium text-white shadow-md hover:shadow-lg transition-all duration-200">
            Remove Player
          </button>
        </div>
      </div>
    `;

    // Add modal to body
    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Handle cancel
    document.getElementById('cancelBtn').addEventListener('click', () => {
      overlay.classList.add('animate-fade-out');
      modal.classList.add('animate-slide-down');
      setTimeout(() => {
        document.body.removeChild(overlay);
      }, 300);
    });

    // Handle click outside modal
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.classList.add('animate-fade-out');
        modal.classList.add('animate-slide-down');
        setTimeout(() => {
          document.body.removeChild(overlay);
        }, 300);
      }
    });

    // Handle confirm
    document.getElementById('confirmBtn').addEventListener('click', () => {
      // Show loading state
      const confirmBtn = document.getElementById('confirmBtn');
      confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Removing...';
      confirmBtn.disabled = true;

      fetch(`/fantasy/teams/${teamId}/players/${rosterId}`, {
        method: 'DELETE',
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message before redirect
          modal.innerHTML = `
            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white py-4 px-6 rounded-t-2xl">
              <h3 class="text-xl font-bold font-heading">Player Removed</h3>
            </div>
            <div class="p-6 text-center">
              <div class="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
              </div>
              <p class="text-secondary-600 mb-6">Player has been removed from your team!</p>
              <p class="text-secondary-500 text-sm">Refreshing page...</p>
            </div>
          `;

          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          overlay.classList.add('animate-fade-out');
          modal.classList.add('animate-slide-down');
          setTimeout(() => {
            document.body.removeChild(overlay);
            showToast(data.error || 'Failed to remove player from team', 'error');
          }, 300);
        }
      })
      .catch(error => {
        console.error('Error removing player from team:', error);
        overlay.classList.add('animate-fade-out');
        modal.classList.add('animate-slide-down');
        setTimeout(() => {
          document.body.removeChild(overlay);
          showToast('An error occurred. Please try again.', 'error');
        }, 300);
      });
    });
  }

  // Toast notification function
  function showToast(message, type = 'info') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    });

    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col space-y-2';
      document.body.appendChild(toastContainer);
    }

    // Create toast
    const toast = document.createElement('div');
    toast.className = 'toast-notification animate-slide-in-right bg-white rounded-xl shadow-lg p-4 flex items-center max-w-md';

    // Set icon and color based on type
    let iconClass, bgClass;
    switch (type) {
      case 'success':
        iconClass = 'fas fa-check-circle text-green-500';
        bgClass = 'bg-green-50 border-l-4 border-green-500';
        break;
      case 'error':
        iconClass = 'fas fa-exclamation-circle text-red-500';
        bgClass = 'bg-red-50 border-l-4 border-red-500';
        break;
      case 'warning':
        iconClass = 'fas fa-exclamation-triangle text-yellow-500';
        bgClass = 'bg-yellow-50 border-l-4 border-yellow-500';
        break;
      default:
        iconClass = 'fas fa-info-circle text-blue-500';
        bgClass = 'bg-blue-50 border-l-4 border-blue-500';
    }

    toast.classList.add(...bgClass.split(' '));

    toast.innerHTML = `
      <div class="flex-shrink-0 mr-3">
        <i class="${iconClass} text-lg"></i>
      </div>
      <div class="flex-grow">
        <p class="text-secondary-700">${message}</p>
      </div>
      <button class="ml-4 text-secondary-400 hover:text-secondary-600 focus:outline-none">
        <i class="fas fa-times"></i>
      </button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Add close functionality
    const closeBtn = toast.querySelector('button');
    closeBtn.addEventListener('click', () => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        toastContainer.removeChild(toast);
      }, 300);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.classList.add('animate-fade-out');
        setTimeout(() => {
          if (toast.parentNode) {
            toastContainer.removeChild(toast);
          }
        }, 300);
      }
    }, 5000);
  }
</script>

<style>
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* Animations */
  @keyframes sectionFocus {
    0% { transform: scale(1); }
    50% { transform: scale(1.01); }
    100% { transform: scale(1); }
  }

  .animate-section-focus {
    animation: sectionFocus 0.5s ease-in-out;
  }

  @keyframes blob-bounce {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(20px, -20px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
  }

  .animate-blob {
    animation: blob-bounce 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out;
  }

  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-out;
  }

  @keyframes slideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideDown {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(50px); opacity: 0; }
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  @keyframes bounceSlight {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
  }

  .animate-bounce-subtle {
    animation: bounceSlight 0.5s infinite;
  }

  /* Player card styles */
  .player-card {
    transition: all 0.3s ease;
  }
</style>
