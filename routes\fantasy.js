const express = require('express');
const router = express.Router();
const db = require('../config/database');
const crypto = require('crypto');

// Middleware to check if user is logged in
const isAuthenticated = (req, res, next) => {
  if (req.session.user) {
    return next();
  }
  res.redirect('/auth/login?redirect=' + req.originalUrl);
};

// Fantasy leagues home page with payment integration
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get active fantasy leagues with payment info
    const [leagues] = await db.query(`
      SELECT fl.*, sc.name as category_name, sc.color_code,
             COUNT(DISTINCT ft.id) as team_count,
             COUNT(DISTINCT ulp.id) as participants_count
      FROM fantasy_leagues fl
      LEFT JOIN sports_categories sc ON fl.category_id = sc.id
      LEFT JOIN fantasy_teams ft ON fl.id = ft.league_id
      LEFT JOIN user_league_participation ulp ON fl.id = ulp.league_id AND ulp.participation_status IN ('paid', 'team_created', 'active')
      WHERE fl.status IN ('upcoming', 'active')
      GROUP BY fl.id
      ORDER BY fl.start_date ASC
    `);

    // Get user's league participation status
    const [userParticipation] = await db.query(`
      SELECT ulp.*, fl.name as league_name, fl.entry_fee, pt.payment_status, pt.transaction_reference
      FROM user_league_participation ulp
      JOIN fantasy_leagues fl ON ulp.league_id = fl.id
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ?
      ORDER BY ulp.joined_at DESC
    `, [req.session.user.id]);

    // Get user's fantasy teams
    const [userTeams] = await db.query(`
      SELECT ft.*, fl.name as league_name, fl.status as league_status,
             ulp.participation_status, ulp.team_budget, ulp.budget_used
      FROM fantasy_teams ft
      JOIN fantasy_leagues fl ON ft.league_id = fl.id
      JOIN user_league_participation ulp ON ft.league_id = ulp.league_id AND ft.user_id = ulp.user_id
      WHERE ft.user_id = ?
      ORDER BY ft.created_at DESC
    `, [req.session.user.id]);

    res.render('pages/fantasy/index', {
      title: 'Fantasy Sports',
      user: req.session.user,
      leagues,
      userParticipation,
      userTeams
    });
  } catch (error) {
    console.error('Error loading fantasy page:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load fantasy leagues'
    });
  }
});

// Join league - Payment step
router.get('/join/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;

    // Get league details
    const [league] = await db.query(`
      SELECT fl.*, sc.name as category_name
      FROM fantasy_leagues fl
      LEFT JOIN sports_categories sc ON fl.category_id = sc.id
      WHERE fl.id = ?
    `, [leagueId]);

    if (league.length === 0) {
      return res.status(404).render('pages/error', {
        title: 'League Not Found',
        message: 'The requested fantasy league does not exist.'
      });
    }

    // Check if user already joined
    const [participation] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (participation.length > 0) {
      return res.redirect(`/fantasy/payment-status/${leagueId}`);
    }

    // Get available payment methods
    const [paymentMethods] = await db.query(
      'SELECT * FROM payment_methods WHERE is_active = TRUE ORDER BY display_order'
    );

    res.render('pages/fantasy/join-league', {
      title: `Join ${league[0].name}`,
      league: league[0],
      paymentMethods,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading join league page:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load league information'
    });
  }
});

// Process payment submission
router.post('/join/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;
    const { paymentMethod, transactionReference, paymentProof } = req.body;

    // Get league details
    const [league] = await db.query('SELECT * FROM fantasy_leagues WHERE id = ?', [leagueId]);
    if (league.length === 0) {
      return res.status(404).json({ error: 'League not found' });
    }

    // Check if user already joined
    const [existing] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: 'You have already joined this league' });
    }

    // Generate unique transaction reference if not provided
    const txnRef = transactionReference || `TXN${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    // Create payment transaction
    const [paymentResult] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [req.session.user.id, leagueId, league[0].entry_fee, paymentMethod, txnRef, paymentProof || 'Payment submitted', 'pending']);

    // Create user league participation
    await db.query(`
      INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
      VALUES (?, ?, ?, 'pending_payment', 100.00)
    `, [req.session.user.id, leagueId, paymentResult.insertId]);

    res.json({
      success: true,
      message: 'Payment submitted successfully! Please wait for admin verification.',
      transactionReference: txnRef,
      redirectUrl: `/fantasy/payment-status/${leagueId}`
    });
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({ error: 'Failed to process payment' });
  }
});

// Payment status page
router.get('/payment-status/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;

    // Get participation and payment details
    const [participation] = await db.query(`
      SELECT ulp.*, fl.name as league_name, fl.entry_fee, pt.payment_status, pt.transaction_reference, pt.payment_method, pt.created_at as payment_date
      FROM user_league_participation ulp
      JOIN fantasy_leagues fl ON ulp.league_id = fl.id
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, leagueId]);

    if (participation.length === 0) {
      return res.redirect(`/fantasy/join/${leagueId}`);
    }

    res.render('pages/fantasy/payment-status', {
      title: 'Payment Status',
      participation: participation[0],
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading payment status:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load payment status'
    });
  }
});

// View single fantasy team
router.get('/teams/:id', isAuthenticated, async (req, res) => {
  try {
    // Get team details
    const [team] = await db.query(
      `SELECT ft.*, fl.name as league_name, fl.category_id, c.name as sport_name
       FROM fantasy_teams ft
       JOIN fantasy_leagues fl ON ft.league_id = fl.id
       LEFT JOIN sports_categories c ON fl.category_id = c.id
       WHERE ft.id = ?`,
      [req.params.id]
    );
    
    if (team.length === 0) {
      return res.status(404).render('pages/error', { 
        title: 'Not Found',
        message: 'Team not found' 
      });
    }
    
    // Check if user owns this team or is admin
    const isOwner = team[0].user_id === req.session.user.id;
    if (!isOwner && !req.session.user.isAdmin) {
      return res.status(403).render('pages/error', {
        title: 'Access Denied',
        message: 'You do not have permission to view this team'
      });
    }

    // Get user's participation status for payment verification
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, team[0].league_id]);

    const canSelectPlayers = participation.length > 0 &&
                            (participation[0].participation_status === 'paid' ||
                             participation[0].participation_status === 'team_created' ||
                             participation[0].participation_status === 'active');

    // Get team players with enhanced selection data
    const [teamPlayers] = await db.query(`
      SELECT p.*, fts.id as selection_id, fts.is_captain, fts.is_vice_captain,
             fts.player_price, t.name as team_name
      FROM fantasy_team_selections fts
      JOIN players p ON fts.player_id = p.id
      JOIN teams t ON p.team_id = t.id
      WHERE fts.user_id = ? AND fts.league_id = ?
      ORDER BY fts.selection_order
    `, [req.session.user.id, team[0].league_id]);

    // Get available players for this sport (only if payment verified)
    let availablePlayers = [];
    if (canSelectPlayers) {
      const [players] = await db.query(`
        SELECT p.*, t.name as team_name
        FROM players p
        JOIN teams t ON p.team_id = t.id
        WHERE t.category_id = ? AND p.is_available = TRUE
        AND p.id NOT IN (
          SELECT player_id FROM fantasy_team_selections
          WHERE user_id = ? AND league_id = ?
        )
        ORDER BY p.price DESC, p.name
        LIMIT 100
      `, [team[0].category_id, req.session.user.id, team[0].league_id]);
      availablePlayers = players;
    }

    res.render('pages/fantasy/team', {
      title: team[0].name,
      team: team[0],
      teamPlayers,
      availablePlayers,
      participation: participation[0] || null,
      canSelectPlayers,
      isOwner,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching fantasy team:', error);
    res.status(500).render('pages/error', { 
      title: 'Error',
      message: 'Failed to load fantasy team' 
    });
  }
});

// Add player to fantasy team with payment verification and budget management
router.post('/teams/:id/players', isAuthenticated, async (req, res) => {
  try {
    const { playerId, position } = req.body;

    if (!playerId || !position) {
      return res.status(400).json({ error: 'Player ID and position are required' });
    }

    // Get team and league info
    const [team] = await db.query(
      'SELECT * FROM fantasy_teams WHERE id = ? AND user_id = ?',
      [req.params.id, req.session.user.id]
    );

    if (team.length === 0) {
      return res.status(403).json({ error: 'You do not have permission to modify this team' });
    }

    // Check payment status
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, team[0].league_id]);

    if (participation.length === 0 || participation[0].participation_status === 'pending_payment') {
      return res.status(403).json({
        error: 'Payment verification required before selecting players',
        requiresPayment: true
      });
    }

    // Get player details
    const [player] = await db.query('SELECT * FROM players WHERE id = ?', [playerId]);
    if (player.length === 0) {
      return res.status(404).json({ error: 'Player not found' });
    }

    // Check budget constraints
    const newBudgetUsed = parseFloat(participation[0].budget_used) + parseFloat(player[0].price);
    if (newBudgetUsed > parseFloat(participation[0].team_budget)) {
      return res.status(400).json({
        error: `Insufficient budget. Player costs $${player[0].price}, you have $${(participation[0].team_budget - participation[0].budget_used).toFixed(2)} remaining.`
      });
    }

    // Check player limit
    if (participation[0].players_selected >= participation[0].max_players) {
      return res.status(400).json({
        error: `Team is full. Maximum ${participation[0].max_players} players allowed.`
      });
    }

    // Add player to team
    const selectionOrder = participation[0].players_selected + 1;
    await db.query(`
      INSERT INTO fantasy_team_selections (user_id, league_id, player_id, position, player_price, selection_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [req.session.user.id, team[0].league_id, playerId, position, player[0].price, selectionOrder]);

    // Update participation budget and player count
    await db.query(`
      UPDATE user_league_participation
      SET budget_used = ?, players_selected = ?, participation_status = 'team_created'
      WHERE user_id = ? AND league_id = ?
    `, [newBudgetUsed, selectionOrder, req.session.user.id, team[0].league_id]);

    res.status(201).json({
      success: true,
      message: `${player[0].name} added to your team!`,
      budgetRemaining: (participation[0].team_budget - newBudgetUsed).toFixed(2),
      playersSelected: selectionOrder
    });
  } catch (error) {
    console.error('Error adding player to fantasy team:', error);
    res.status(500).json({ error: 'Failed to add player' });
  }
});

// Remove player from fantasy team with budget adjustment
router.delete('/teams/:teamId/players/:selectionId', isAuthenticated, async (req, res) => {
  try {
    // Get selection details
    const [selection] = await db.query(`
      SELECT fts.*, ft.league_id
      FROM fantasy_team_selections fts
      JOIN fantasy_teams ft ON fts.league_id = ft.league_id
      WHERE fts.id = ? AND fts.user_id = ? AND ft.id = ?
    `, [req.params.selectionId, req.session.user.id, req.params.teamId]);

    if (selection.length === 0) {
      return res.status(403).json({ error: 'You do not have permission to modify this selection' });
    }

    // Get current participation
    const [participation] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, selection[0].league_id]
    );

    if (participation.length === 0) {
      return res.status(404).json({ error: 'Participation record not found' });
    }

    // Remove player from team
    await db.query('DELETE FROM fantasy_team_selections WHERE id = ?', [req.params.selectionId]);

    // Update participation budget and player count
    const newBudgetUsed = parseFloat(participation[0].budget_used) - parseFloat(selection[0].player_price);
    const newPlayersSelected = participation[0].players_selected - 1;

    await db.query(`
      UPDATE user_league_participation
      SET budget_used = ?, players_selected = ?
      WHERE user_id = ? AND league_id = ?
    `, [Math.max(0, newBudgetUsed), Math.max(0, newPlayersSelected), req.session.user.id, selection[0].league_id]);

    res.json({
      success: true,
      message: 'Player removed from team',
      budgetRemaining: (participation[0].team_budget - newBudgetUsed).toFixed(2),
      playersSelected: newPlayersSelected
    });
  } catch (error) {
    console.error('Error removing player from fantasy team:', error);
    res.status(500).json({ error: 'Failed to remove player' });
  }
});

// Admin route to verify payments
router.post('/admin/verify-payment/:transactionId', isAuthenticated, async (req, res) => {
  try {
    // Simple admin check (you can enhance this)
    if (!req.session.user.username.includes('admin')) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { status, notes } = req.body;

    // Update payment status
    await db.query(`
      UPDATE payment_transactions
      SET payment_status = ?, admin_notes = ?, verified_at = NOW(), verified_by = ?
      WHERE id = ?
    `, [status, notes, req.session.user.id, req.params.transactionId]);

    // Update participation status if payment verified
    if (status === 'verified') {
      await db.query(`
        UPDATE user_league_participation ulp
        JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
        SET ulp.participation_status = 'paid'
        WHERE pt.id = ?
      `, [req.params.transactionId]);
    }

    res.json({ success: true, message: 'Payment status updated' });
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({ error: 'Failed to verify payment' });
  }
});

module.exports = router;
