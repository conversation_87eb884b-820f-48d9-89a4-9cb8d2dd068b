const express = require('express');
const path = require('path');
const fs = require('fs');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const cors = require('cors');
const expressLayouts = require('express-ejs-layouts');
require('dotenv').config();

// Import routes
const indexRoutes = require('./routes/index');
const newsRoutes = require('./routes/news');
const streamingRoutes = require('./routes/streaming');
const fantasyRoutes = require('./routes/fantasy');
const authRoutes = require('./routes/auth');

// Initialize express app
const app = express();
const PORT = process.env.PORT || 3000;

// Import MySQL database connection
const db = require('./config/database');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

// Session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
  }
};

// Use MySQL session store in production, memory store in development
if (process.env.NODE_ENV === 'production') {
  try {
    const MySQLStore = require('express-mysql-session')(session);

    const options = {
      host: process.env.DB_HOST || 'localhost',
      port: 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sports_website'
    };

    console.log('Using MySQL session store');
    sessionConfig.store = new MySQLStore(options);
  } catch (error) {
    console.error('Failed to initialize MySQL session store:', error.message);
    console.log('Falling back to memory store (not recommended for production)');

    // Add a warning about memory leaks in production
    if (process.env.NODE_ENV === 'production') {
      console.warn('WARNING: Using memory store in production can cause memory leaks!');
    }
  }
} else {
  console.log('Using default memory session store (development mode)');
}

app.use(session(sessionConfig));

// Set view engine
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

// Set up EJS layouts
app.use(expressLayouts);
app.set('layout', 'layouts/main');
app.set("layout extractScripts", true);

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Middleware to catch errors in routes
const asyncHandler = fn => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Wrap route handlers with error catching
const wrapRoutes = (router) => {
  const originalUse = router.use.bind(router);
  router.use = function(path, ...handlers) {
    if (typeof path === 'function') {
      handlers.unshift(path);
      path = '/';
    }

    const wrappedHandlers = handlers.map(handler => {
      if (typeof handler === 'function') {
        return asyncHandler(handler);
      }
      return handler;
    });

    return originalUse(path, ...wrappedHandlers);
  };
  return router;
};

// Routes with error handling
app.use('/', wrapRoutes(indexRoutes));
app.use('/news', wrapRoutes(newsRoutes));
app.use('/streaming', wrapRoutes(streamingRoutes));
app.use('/fantasy', wrapRoutes(fantasyRoutes));
app.use('/auth', wrapRoutes(authRoutes));

// Add a health check route for debugging
app.get('/health', (req, res) => {
  const health = {
    uptime: process.uptime(),
    timestamp: Date.now(),
    env: process.env.NODE_ENV,
    database: 'MySQL',
    session_store: sessionConfig.store ? 'MySQL' : 'Memory'
  };

  res.status(200).json(health);
});

// 404 handler
app.use((req, res, next) => {
  res.status(404).render('pages/error', {
    title: '404 - Page Not Found',
    message: 'The page you are looking for does not exist.'
  });
});

// Error handler
app.use((err, req, res, next) => {
  // Log detailed error information
  console.error('SERVER ERROR:', err.message);
  console.error('Error Stack:', err.stack);

  // Log request details that might help debugging
  console.error('Request URL:', req.originalUrl);
  console.error('Request Method:', req.method);
  console.error('Request Headers:', JSON.stringify(req.headers, null, 2));

  // Try to send a response with multiple fallbacks
  try {
    // First try: Render the error page using EJS
    res.status(500).render('pages/error', {
      title: '500 - Server Error',
      message: 'Something went wrong on our end. Error: ' + err.message
    });
  } catch (renderError) {
    console.error('Error rendering EJS error page:', renderError);

    try {
      // Second try: Redirect to static error page with error details
      const errorParam = encodeURIComponent(err.message);
      res.redirect(`/error.html?error=${errorParam}`);
    } catch (redirectError) {
      console.error('Error redirecting to static error page:', redirectError);

      try {
        // Third try: Send the static error page directly
        const errorHtmlPath = path.join(__dirname, 'public', 'error.html');
        res.status(500).sendFile(errorHtmlPath);
      } catch (sendFileError) {
        console.error('Error sending static error file:', sendFileError);

        // Last resort: Send a simple text response
        res.status(500).send('500 Server Error: ' + err.message);
      }
    }
  }
});

// Check if error page exists
const errorHtmlPath = path.join(__dirname, 'public', 'error.html');
if (!fs.existsSync(errorHtmlPath)) {
  console.warn('Warning: Static error page not found at', errorHtmlPath);
}

// Start server on all network interfaces
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on:`);
  console.log(`   Local:    http://localhost:${PORT}`);
  console.log(`   Network:  http://**************:${PORT}`);
  console.log(`   Health:   http://**************:${PORT}/health`);
  console.log(`📧 Password reset emails will use: ${process.env.WEBSITE_URL || 'http://localhost:' + PORT}`);
});
