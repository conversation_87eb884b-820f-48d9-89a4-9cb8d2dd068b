-- Fantasy Payment and Team Selection System Database Schema

-- Payment Transactions Table
CREATE TABLE payment_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    league_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bank_transfer', 'cash', 'mobile_money', 'credit') DEFAULT 'bank_transfer',
    transaction_reference VARCHAR(100) UNIQUE NOT NULL,
    payment_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    payment_proof TEXT, -- Store file path or description
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL,
    verified_by INT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_payment (user_id),
    INDEX idx_league_payment (league_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_transaction_ref (transaction_reference)
);

-- User League Participation Table
CREATE TABLE user_league_participation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    league_id INT NOT NULL,
    payment_transaction_id INT,
    participation_status ENUM('pending_payment', 'paid', 'team_created', 'active') DEFAULT 'pending_payment',
    team_budget DECIMAL(8,2) DEFAULT 100.00,
    budget_used DECIMAL(8,2) DEFAULT 0.00,
    players_selected INT DEFAULT 0,
    max_players INT DEFAULT 11,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_transaction_id) REFERENCES payment_transactions(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_league (user_id, league_id),
    INDEX idx_user_participation (user_id),
    INDEX idx_league_participation (league_id),
    INDEX idx_participation_status (participation_status)
);

-- Fantasy Team Selections Table (Enhanced)
CREATE TABLE fantasy_team_selections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    league_id INT NOT NULL,
    player_id INT NOT NULL,
    position VARCHAR(50) NOT NULL,
    is_captain BOOLEAN DEFAULT FALSE,
    is_vice_captain BOOLEAN DEFAULT FALSE,
    player_price DECIMAL(8,2) NOT NULL,
    selection_order INT,
    selected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (league_id) REFERENCES fantasy_leagues(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_league_player (user_id, league_id, player_id),
    INDEX idx_user_selections (user_id, league_id),
    INDEX idx_player_selections (player_id),
    INDEX idx_position_selections (position)
);

-- Payment Methods Configuration
CREATE TABLE payment_methods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    method_name VARCHAR(50) NOT NULL,
    method_type ENUM('bank_transfer', 'cash', 'mobile_money', 'credit') NOT NULL,
    account_details JSON,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    instructions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_method_type (method_type),
    INDEX idx_active_methods (is_active)
);

-- Insert default payment methods
INSERT INTO payment_methods (method_name, method_type, account_details, instructions, display_order) VALUES
('Bank Transfer', 'bank_transfer', '{"bank_name": "Sports Bank", "account_number": "**********", "account_name": "Online Sports Website", "routing_number": "123456"}', 'Transfer the league entry fee to our bank account and upload the receipt.', 1),
('Cash Payment', 'cash', '{"contact_person": "Sports Admin", "phone": "+**********", "address": "123 Sports Street, City"}', 'Contact our admin to arrange cash payment at our office.', 2),
('Mobile Money', 'mobile_money', '{"service": "SportsPay", "number": "+**********", "name": "Sports Admin"}', 'Send payment via mobile money and provide transaction ID.', 3),
('Credit System', 'credit', '{"description": "Internal credit system"}', 'Use your account credits to pay for league entry.', 4);

-- Sample fantasy league with entry fee
UPDATE fantasy_leagues SET entry_fee = 25.00, prize_pool = 500.00 WHERE id = 1;

-- Add some sample payment transactions
INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_status, payment_proof) VALUES
(1, 1, 25.00, 'bank_transfer', 'TXN001', 'verified', 'Bank receipt uploaded'),
(2, 1, 25.00, 'mobile_money', 'TXN002', 'pending', 'Mobile money transaction ID: MM123456'),
(3, 1, 25.00, 'cash', 'TXN003', 'verified', 'Cash received at office');

-- Add user league participation records
INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget) VALUES
(1, 1, 1, 'paid', 100.00),
(2, 1, 2, 'pending_payment', 100.00),
(3, 1, 3, 'paid', 100.00);
