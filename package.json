{"name": "online-sports-website", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build:css": "tailwindcss -i ./public/css/style.css -o ./public/css/output.css", "watch:css": "tailwindcss -i ./public/css/style.css -o ./public/css/output.css --watch", "init-db": "node init-db.js", "import-sample-data": "node import-sample-data.js", "init-session": "node init-mysql-session.js", "setup-db": "npm run init-db && npm run import-sample-data && npm run init-session", "update-ip": "node update-ip.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-mysql-session": "^3.0.3", "express-session": "^1.18.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.3", "tailwindcss": "^4.1.7"}}